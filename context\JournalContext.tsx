import AsyncStorage from '@react-native-async-storage/async-storage';
import React, { createContext, useContext, useEffect, useState } from 'react';

// Define the shape of a journal entry
export interface JournalEntry {
  id: string;
  date: string;
  title: string;
  content: string;
  mood?: string;
  preview?: string;
}

// Define the context shape
interface JournalContextType {
  entries: JournalEntry[];
  addEntry: (entry: Omit<JournalEntry, 'id'>) => Promise<JournalEntry>;
  updateEntry: (entry: JournalEntry) => Promise<void>;
  deleteEntry: (id: string) => Promise<void>;
  getEntry: (id: string) => JournalEntry | undefined;
  loading: boolean;
}

// Create the context
const JournalContext = createContext<JournalContextType | undefined>(undefined);

// Storage key
const STORAGE_KEY = 'journal_entries';

// Provider component
export function JournalProvider({ children }: { children: React.ReactNode }) {
  const [entries, setEntries] = useState<JournalEntry[]>([]);
  const [loading, setLoading] = useState(true);

  // Load entries from storage on mount
  useEffect(() => {
    const loadEntries = async () => {
      try {
        const storedEntries = await AsyncStorage.getItem(STORAGE_KEY);
        if (storedEntries) {
          setEntries(JSON.parse(storedEntries));
        }
      } catch (error) {
        console.error('Failed to load entries:', error);
      } finally {
        setLoading(false);
      }
    };

    loadEntries();
  }, []);

  // Save entries to storage whenever they change
  useEffect(() => {
    const saveEntries = async () => {
      try {
        await AsyncStorage.setItem(STORAGE_KEY, JSON.stringify(entries));
      } catch (error) {
        console.error('Failed to save entries:', error);
      }
    };

    if (!loading) {
      saveEntries();
    }
  }, [entries, loading]);

  // Add a new entry
  const addEntry = async (entryData: Omit<JournalEntry, 'id'>): Promise<JournalEntry> => {
    const newEntry: JournalEntry = {
      ...entryData,
      id: Date.now().toString(),
      preview: entryData.content.substring(0, 100) + (entryData.content.length > 100 ? '...' : '')
    };
    
    setEntries(prevEntries => [newEntry, ...prevEntries]);
    return newEntry;
  };

  // Update an existing entry
  const updateEntry = async (updatedEntry: JournalEntry): Promise<void> => {
    setEntries(prevEntries => 
      prevEntries.map(entry => 
        entry.id === updatedEntry.id 
          ? {
              ...updatedEntry,
              preview: updatedEntry.content.substring(0, 100) + (updatedEntry.content.length > 100 ? '...' : '')
            } 
          : entry
      )
    );
  };

  // Delete an entry
  const deleteEntry = async (id: string): Promise<void> => {
    setEntries(prevEntries => prevEntries.filter(entry => entry.id !== id));
  };

  // Get a specific entry
  const getEntry = (id: string): JournalEntry | undefined => {
    return entries.find(entry => entry.id === id);
  };

  return (
    <JournalContext.Provider
      value={{
        entries,
        addEntry,
        updateEntry,
        deleteEntry,
        getEntry,
        loading
      }}
    >
      {children}
    </JournalContext.Provider>
  );
}

// Custom hook to use the journal context
export function useJournal() {
  const context = useContext(JournalContext);
  if (context === undefined) {
    throw new Error('useJournal must be used within a JournalProvider');
  }
  return context;
}
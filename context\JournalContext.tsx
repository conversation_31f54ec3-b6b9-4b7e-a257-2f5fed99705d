import AsyncStorage from '@react-native-async-storage/async-storage';
import React, { createContext, useContext, useEffect, useState } from 'react';

// Define the shape of a time-based activity
export interface TimeActivity {
  id: string;
  startTime: string; // HH:MM format
  endTime: string;   // HH:MM format
  activity: string;
  description?: string;
  category: 'work' | 'personal' | 'health' | 'social' | 'entertainment' | 'travel' | 'food' | 'exercise' | 'sleep' | 'other';
  mood?: 'excellent' | 'good' | 'neutral' | 'bad' | 'terrible';
  energy?: 'high' | 'medium' | 'low';
  productivity?: number; // 1-5 scale
  tags?: string[];
  photos?: string[];
  location?: string;
  notes?: string;
}

// Define the shape of a daily journal entry
export interface DailyJournal {
  id: string;
  date: string; // YYYY-MM-DD format
  activities: TimeActivity[];
  dailyReflection?: string;
  overallMood?: 'excellent' | 'good' | 'neutral' | 'bad' | 'terrible';
  gratitude?: string[];
  goals?: string[];
  achievements?: string[];
  challenges?: string[];
  totalProductiveHours?: number;
  sleepHours?: number;
  exerciseMinutes?: number;
  createdAt: string;
  updatedAt: string;
}

// Legacy interface for backward compatibility
export interface JournalEntry {
  id: string;
  date: string;
  title: string;
  content: string;
  mood?: string;
  preview?: string;
  tags?: string[];
  photos?: string[];
  audioRecording?: string;
  drawing?: string;
  isPasswordProtected?: boolean;
  passwordHash?: string;
  wordCount?: number;
  readingTime?: number;
  weather?: {
    condition: string;
    temperature: number;
    location: string;
  };
  location?: {
    name: string;
    coordinates: {
      latitude: number;
      longitude: number;
    };
  };
  createdAt: string;
  updatedAt: string;
}

// Define the context shape
interface JournalContextType {
  // Simple activity management
  activities: any[];
  saveActivity: (activity: any) => Promise<any>;
  getActivitiesForDate: (date: string) => any[];

  // Daily journals
  dailyJournals: DailyJournal[];
  getTodayJournal: () => DailyJournal | undefined;
  createDailyJournal: (date: string) => Promise<DailyJournal>;
  updateDailyJournal: (journal: DailyJournal) => Promise<void>;

  // Time activities
  addTimeActivity: (date: string, activity: Omit<TimeActivity, 'id'>) => Promise<void>;
  updateTimeActivity: (date: string, activity: TimeActivity) => Promise<void>;
  deleteTimeActivity: (date: string, activityId: string) => Promise<void>;

  // Analytics
  getWeeklyStats: (weekStart: string) => WeeklyStats;
  getMonthlyStats: (month: string) => MonthlyStats;
  getCategoryTimeSpent: (startDate: string, endDate: string) => CategoryStats[];
  getProductivityTrends: (days: number) => ProductivityTrend[];
  getMoodTrends: (days: number) => MoodTrend[];

  // Legacy support
  entries: JournalEntry[];
  addEntry: (entry: Omit<JournalEntry, 'id' | 'createdAt' | 'updatedAt'>) => Promise<JournalEntry>;
  updateEntry: (entry: JournalEntry) => Promise<void>;
  deleteEntry: (id: string) => Promise<void>;
  getEntry: (id: string) => JournalEntry | undefined;
  searchEntries: (query: string) => JournalEntry[];
  getEntriesByTag: (tag: string) => JournalEntry[];
  getEntriesByMood: (mood: string) => JournalEntry[];
  getEntriesByDateRange: (startDate: string, endDate: string) => JournalEntry[];
  getMoodStats: () => { [mood: string]: number };
  getWritingStreak: () => number;
  getTotalWordCount: () => number;
  exportEntries: (format: 'json' | 'text') => Promise<string>;
  loading: boolean;
}

// Analytics interfaces
export interface WeeklyStats {
  totalHours: number;
  productiveHours: number;
  categoryBreakdown: CategoryStats[];
  averageMood: number;
  averageEnergy: number;
  topActivities: string[];
}

export interface MonthlyStats {
  totalDays: number;
  activeDays: number;
  totalHours: number;
  productiveHours: number;
  categoryBreakdown: CategoryStats[];
  moodDistribution: { [mood: string]: number };
  productivityAverage: number;
  topCategories: string[];
  achievements: string[];
}

export interface CategoryStats {
  category: string;
  hours: number;
  percentage: number;
  color: string;
}

export interface ProductivityTrend {
  date: string;
  productivity: number;
  hours: number;
}

export interface MoodTrend {
  date: string;
  mood: number; // 1-5 scale
  activities: number;
}

// Create the context
const JournalContext = createContext<JournalContextType | undefined>(undefined);

// Storage key
const STORAGE_KEY = 'journal_entries';

// Provider component
export function JournalProvider({ children }: { children: React.ReactNode }) {
  const [entries, setEntries] = useState<JournalEntry[]>([]);
  const [loading, setLoading] = useState(true);

  // Load entries from storage on mount
  useEffect(() => {
    const loadEntries = async () => {
      try {
        const storedEntries = await AsyncStorage.getItem(STORAGE_KEY);
        if (storedEntries) {
          setEntries(JSON.parse(storedEntries));
        }
      } catch (error) {
        console.error('Failed to load entries:', error);
      } finally {
        setLoading(false);
      }
    };

    loadEntries();
  }, []);

  // Save entries to storage whenever they change
  useEffect(() => {
    const saveEntries = async () => {
      try {
        await AsyncStorage.setItem(STORAGE_KEY, JSON.stringify(entries));
      } catch (error) {
        console.error('Failed to save entries:', error);
      }
    };

    if (!loading) {
      saveEntries();
    }
  }, [entries, loading]);

  // Helper function to calculate word count
  const calculateWordCount = (text: string): number => {
    return text.trim().split(/\s+/).filter(word => word.length > 0).length;
  };

  // Helper function to calculate reading time (average 200 words per minute)
  const calculateReadingTime = (wordCount: number): number => {
    return Math.ceil(wordCount / 200);
  };

  // Add a new entry
  const addEntry = async (entryData: Omit<JournalEntry, 'id' | 'createdAt' | 'updatedAt'>): Promise<JournalEntry> => {
    const wordCount = calculateWordCount(entryData.content);
    const now = new Date().toISOString();

    const newEntry: JournalEntry = {
      ...entryData,
      id: Date.now().toString(),
      preview: entryData.content.substring(0, 100) + (entryData.content.length > 100 ? '...' : ''),
      wordCount,
      readingTime: calculateReadingTime(wordCount),
      createdAt: now,
      updatedAt: now,
    };

    setEntries(prevEntries => [newEntry, ...prevEntries]);
    return newEntry;
  };

  // Update an existing entry
  const updateEntry = async (updatedEntry: JournalEntry): Promise<void> => {
    const wordCount = calculateWordCount(updatedEntry.content);

    setEntries(prevEntries =>
      prevEntries.map(entry =>
        entry.id === updatedEntry.id
          ? {
              ...updatedEntry,
              preview: updatedEntry.content.substring(0, 100) + (updatedEntry.content.length > 100 ? '...' : ''),
              wordCount,
              readingTime: calculateReadingTime(wordCount),
              updatedAt: new Date().toISOString(),
            }
          : entry
      )
    );
  };

  // Delete an entry
  const deleteEntry = async (id: string): Promise<void> => {
    setEntries(prevEntries => prevEntries.filter(entry => entry.id !== id));
  };

  // Get a specific entry
  const getEntry = (id: string): JournalEntry | undefined => {
    return entries.find(entry => entry.id === id);
  };

  // Search entries by title, content, or tags
  const searchEntries = (query: string): JournalEntry[] => {
    const lowercaseQuery = query.toLowerCase();
    return entries.filter(entry =>
      entry.title.toLowerCase().includes(lowercaseQuery) ||
      entry.content.toLowerCase().includes(lowercaseQuery) ||
      entry.tags?.some(tag => tag.toLowerCase().includes(lowercaseQuery))
    );
  };

  // Get entries by tag
  const getEntriesByTag = (tag: string): JournalEntry[] => {
    return entries.filter(entry => entry.tags?.includes(tag));
  };

  // Get entries by mood
  const getEntriesByMood = (mood: string): JournalEntry[] => {
    return entries.filter(entry => entry.mood === mood);
  };

  // Get entries by date range
  const getEntriesByDateRange = (startDate: string, endDate: string): JournalEntry[] => {
    return entries.filter(entry => {
      const entryDate = new Date(entry.createdAt);
      const start = new Date(startDate);
      const end = new Date(endDate);
      return entryDate >= start && entryDate <= end;
    });
  };

  // Get mood statistics
  const getMoodStats = (): { [mood: string]: number } => {
    const stats: { [mood: string]: number } = {};
    entries.forEach(entry => {
      if (entry.mood) {
        stats[entry.mood] = (stats[entry.mood] || 0) + 1;
      }
    });
    return stats;
  };

  // Get writing streak (consecutive days with entries)
  const getWritingStreak = (): number => {
    if (entries.length === 0) return 0;

    const sortedEntries = [...entries].sort((a, b) =>
      new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime()
    );

    let streak = 0;
    let currentDate = new Date();
    currentDate.setHours(0, 0, 0, 0);

    for (const entry of sortedEntries) {
      const entryDate = new Date(entry.createdAt);
      entryDate.setHours(0, 0, 0, 0);

      const daysDiff = Math.floor((currentDate.getTime() - entryDate.getTime()) / (1000 * 60 * 60 * 24));

      if (daysDiff === streak) {
        streak++;
        currentDate.setDate(currentDate.getDate() - 1);
      } else if (daysDiff > streak) {
        break;
      }
    }

    return streak;
  };

  // Get total word count across all entries
  const getTotalWordCount = (): number => {
    return entries.reduce((total, entry) => total + (entry.wordCount || 0), 0);
  };

  // Export entries in different formats
  const exportEntries = async (format: 'json' | 'text'): Promise<string> => {
    if (format === 'json') {
      return JSON.stringify(entries, null, 2);
    } else {
      return entries.map(entry =>
        `Date: ${entry.date}\nTitle: ${entry.title}\nMood: ${entry.mood || 'Not specified'}\n\n${entry.content}\n\n---\n\n`
      ).join('');
    }
  };

  // Simple activity storage
  const [activities, setActivities] = useState<any[]>([]);

  // Load activities from storage
  useEffect(() => {
    loadActivities();
  }, []);

  const loadActivities = async () => {
    try {
      const stored = await AsyncStorage.getItem('daily_activities');
      if (stored) {
        setActivities(JSON.parse(stored));
      }
    } catch (error) {
      console.error('Error loading activities:', error);
    }
  };

  const saveActivity = async (activity: any) => {
    try {
      const newActivity = {
        ...activity,
        id: Date.now().toString(),
        createdAt: new Date().toISOString(),
      };
      const updatedActivities = [...activities, newActivity];
      setActivities(updatedActivities);
      await AsyncStorage.setItem('daily_activities', JSON.stringify(updatedActivities));
      return newActivity;
    } catch (error) {
      console.error('Error saving activity:', error);
      throw error;
    }
  };

  const getActivitiesForDate = (date: string) => {
    return activities.filter(activity => activity.date === date);
  };

  // Placeholder implementations for new daily journal functions
  const dailyJournals: DailyJournal[] = [];
  const getTodayJournal = () => undefined;
  const createDailyJournal = async (date: string) => ({
    id: Date.now().toString(),
    date,
    activities: [],
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
  } as DailyJournal);
  const updateDailyJournal = async (journal: DailyJournal) => {};
  const addTimeActivity = async (date: string, activity: Omit<TimeActivity, 'id'>) => {
    return await saveActivity({ ...activity, date });
  };
  const updateTimeActivity = async (date: string, activity: TimeActivity) => {};
  const deleteTimeActivity = async (date: string, activityId: string) => {};
  const getWeeklyStats = (weekStart: string) => ({
    totalHours: 0,
    productiveHours: 0,
    categoryBreakdown: [],
    averageMood: 0,
    averageEnergy: 0,
    topActivities: [],
  } as WeeklyStats);
  const getMonthlyStats = (month: string) => ({
    totalDays: 0,
    activeDays: 0,
    totalHours: 0,
    productiveHours: 0,
    categoryBreakdown: [],
    moodDistribution: {},
    productivityAverage: 0,
    topCategories: [],
    achievements: [],
  } as MonthlyStats);
  const getCategoryTimeSpent = (startDate: string, endDate: string) => [] as CategoryStats[];
  const getProductivityTrends = (days: number) => [] as ProductivityTrend[];
  const getMoodTrends = (days: number) => [] as MoodTrend[];

  return (
    <JournalContext.Provider
      value={{
        // Simple activity management
        activities,
        saveActivity,
        getActivitiesForDate,
        // Daily journals
        dailyJournals,
        getTodayJournal,
        createDailyJournal,
        updateDailyJournal,
        addTimeActivity,
        updateTimeActivity,
        deleteTimeActivity,
        getWeeklyStats,
        getMonthlyStats,
        getCategoryTimeSpent,
        getProductivityTrends,
        getMoodTrends,
        // Legacy support
        entries,
        addEntry,
        updateEntry,
        deleteEntry,
        getEntry,
        searchEntries,
        getEntriesByTag,
        getEntriesByMood,
        getEntriesByDateRange,
        getMoodStats,
        getWritingStreak,
        getTotalWordCount,
        exportEntries,
        loading
      }}
    >
      {children}
    </JournalContext.Provider>
  );
}

// Custom hook to use the journal context
export function useJournal() {
  const context = useContext(JournalContext);
  if (context === undefined) {
    throw new Error('useJournal must be used within a JournalProvider');
  }
  return context;
}
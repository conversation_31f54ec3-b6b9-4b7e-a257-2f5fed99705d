import { router } from 'expo-router';
import { StatusBar } from 'expo-status-bar';
import { useState } from 'react';
import { Alert, KeyboardAvoidingView, Platform, Pressable, ScrollView, StyleSheet, TextInput } from 'react-native';

import { ThemedText } from '@/components/ThemedText';
import { ThemedView } from '@/components/ThemedView';
import { Colors } from '@/constants/Colors';
import { useJournal } from '@/context/JournalContext';
import { useColorScheme } from '@/hooks/useColorScheme';

// Mood options for the journal
const MOOD_OPTIONS = [
  { value: 'happy', label: '😊', description: 'Happy' },
  { value: 'sad', label: '😔', description: 'Sad' },
  { value: 'excited', label: '🤩', description: 'Excited' },
  { value: 'anxious', label: '😰', description: 'Anxious' },
  { value: 'calm', label: '😌', description: 'Calm' },
];

export default function NewEntryScreen() {
  const { addEntry } = useJournal();
  const [title, setTitle] = useState('');
  const [content, setContent] = useState('');
  const [selectedMood, setSelectedMood] = useState('');
  const [tags, setTags] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const colorScheme = useColorScheme();
  const colors = Colors[colorScheme ?? 'light'];

  const handleSave = async () => {
    if (!title.trim() || !content.trim()) {
      Alert.alert('Missing Information', 'Please fill in both title and content.');
      return;
    }

    setIsLoading(true);
    try {
      const tagArray = tags.split(',').map(tag => tag.trim()).filter(tag => tag.length > 0);

      await addEntry({
        title: title.trim(),
        content: content.trim(),
        mood: selectedMood || undefined,
        tags: tagArray.length > 0 ? tagArray : undefined,
        date: new Date().toLocaleDateString('en-US', {
          weekday: 'long',
          year: 'numeric',
          month: 'long',
          day: 'numeric'
        }),
      });

      router.back();
    } catch (error) {
      Alert.alert('Error', 'Failed to save entry. Please try again.');
      console.error('Error saving entry:', error);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <KeyboardAvoidingView 
      style={styles.container}
      behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
      keyboardVerticalOffset={Platform.OS === 'ios' ? 60 : 0}
    >
      <StatusBar style="auto" />
      
      <ThemedView style={styles.header}>
        <Pressable onPress={() => router.back()} style={styles.backButton}>
          <ThemedText style={styles.backButtonText}>Cancel</ThemedText>
        </Pressable>
        <ThemedText style={styles.headerTitle}>New Entry</ThemedText>
        <Pressable 
          onPress={handleSave} 
          style={[styles.saveButton, (!title || !content) && styles.saveButtonDisabled]}
          disabled={!title || !content}
        >
          <ThemedText style={styles.saveButtonText}>Save</ThemedText>
        </Pressable>
      </ThemedView>
      
      <ScrollView style={styles.content}>
        <ThemedText style={styles.dateText}>
          {new Date().toLocaleDateString('en-US', { 
            weekday: 'long', 
            year: 'numeric', 
            month: 'long', 
            day: 'numeric' 
          })}
        </ThemedText>
        
        <TextInput
          style={styles.titleInput}
          placeholder="Title"
          placeholderTextColor="#999"
          value={title}
          onChangeText={setTitle}
        />
        
        <ThemedView style={styles.moodSelector}>
          <ThemedText style={styles.moodLabel}>How are you feeling?</ThemedText>
          <ThemedView style={styles.moodOptions}>
            {MOOD_OPTIONS.map((mood) => (
              <Pressable
                key={mood.value}
                style={[
                  styles.moodOption,
                  selectedMood === mood.value && styles.selectedMoodOption
                ]}
                onPress={() => setSelectedMood(mood.value)}
              >
                <ThemedText style={styles.moodEmoji}>{mood.label}</ThemedText>
                <ThemedText style={styles.moodDescription}>{mood.description}</ThemedText>
              </Pressable>
            ))}
          </ThemedView>
        </ThemedView>
        
        <TextInput
          style={styles.contentInput}
          placeholder="Write your thoughts here..."
          placeholderTextColor="#999"
          multiline
          textAlignVertical="top"
          value={content}
          onChangeText={setContent}
        />
      </ScrollView>
    </KeyboardAvoidingView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#fff',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingTop: 60,
    paddingBottom: 15,
    borderBottomWidth: 1,
    borderBottomColor: '#eee',
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: 'bold',
  },
  backButton: {
    padding: 5,
  },
  backButtonText: {
    fontSize: 16,
    color: '#A1CEDC',
  },
  saveButton: {
    padding: 5,
  },
  saveButtonText: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#A1CEDC',
  },
  saveButtonDisabled: {
    opacity: 0.5,
  },
  content: {
    flex: 1,
    padding: 20,
  },
  dateText: {
    fontSize: 14,
    color: '#888',
    marginBottom: 15,
  },
  titleInput: {
    fontSize: 24,
    fontWeight: 'bold',
    marginBottom: 20,
    padding: 0,
  },
  moodSelector: {
    marginBottom: 20,
  },
  moodLabel: {
    fontSize: 16,
    marginBottom: 10,
  },
  moodOptions: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  moodOption: {
    alignItems: 'center',
    padding: 10,
    borderRadius: 10,
    borderWidth: 1,
    borderColor: '#eee',
    width: '18%',
  },
  selectedMoodOption: {
    backgroundColor: '#f0f9ff',
    borderColor: '#A1CEDC',
  },
  moodEmoji: {
    fontSize: 24,
    marginBottom: 5,
  },
  moodDescription: {
    fontSize: 12,
  },
  contentInput: {
    fontSize: 16,
    lineHeight: 24,
    minHeight: 300,
    padding: 0,
  },
});
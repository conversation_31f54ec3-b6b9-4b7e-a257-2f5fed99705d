import { LinearGradient } from 'expo-linear-gradient';
import { router } from 'expo-router';
import { StatusBar } from 'expo-status-bar';
import React, { useState } from 'react';
import { Alert, Pressable, ScrollView, StyleSheet, TextInput, View } from 'react-native';

import { Card } from '@/components/Card';
import { GradientBackground } from '@/components/GradientBackground';
import { ThemedText } from '@/components/ThemedText';
import { useColorScheme } from '@/hooks/useColorScheme';

const CATEGORIES = {
  work: { color: '#3B82F6', icon: '💼', label: 'Work', gradient: ['#3B82F6', '#1D4ED8'] },
  personal: { color: '#10B981', icon: '🏠', label: 'Personal', gradient: ['#10B981', '#059669'] },
  health: { color: '#F59E0B', icon: '🏃‍♂️', label: 'Health', gradient: ['#F59E0B', '#D97706'] },
  social: { color: '#EF4444', icon: '👥', label: 'Social', gradient: ['#EF4444', '#DC2626'] },
  entertainment: { color: '#8B5CF6', icon: '🎬', label: 'Entertainment', gradient: ['#8B5CF6', '#7C3AED'] },
  travel: { color: '#06B6D4', icon: '✈️', label: 'Travel', gradient: ['#06B6D4', '#0891B2'] },
  food: { color: '#F97316', icon: '🍽️', label: 'Food', gradient: ['#F97316', '#EA580C'] },
  exercise: { color: '#84CC16', icon: '💪', label: 'Exercise', gradient: ['#84CC16', '#65A30D'] },
  sleep: { color: '#6366F1', icon: '😴', label: 'Sleep', gradient: ['#6366F1', '#4F46E5'] },
  other: { color: '#6B7280', icon: '📝', label: 'Other', gradient: ['#6B7280', '#4B5563'] },
};

const MOODS = [
  { value: 'excellent', emoji: '😄', label: 'Excellent', color: '#10B981' },
  { value: 'good', emoji: '😊', label: 'Good', color: '#84CC16' },
  { value: 'neutral', emoji: '😐', label: 'Neutral', color: '#F59E0B' },
  { value: 'bad', emoji: '😔', label: 'Bad', color: '#EF4444' },
  { value: 'terrible', emoji: '😢', label: 'Terrible', color: '#DC2626' },
];

const ENERGY_LEVELS = [
  { value: 'high', label: 'High', color: '#10B981', bars: 3 },
  { value: 'medium', label: 'Medium', color: '#F59E0B', bars: 2 },
  { value: 'low', label: 'Low', color: '#EF4444', bars: 1 },
];

export default function AddActivityScreen() {
  const [activity, setActivity] = useState('');
  const [description, setDescription] = useState('');
  const [startTime, setStartTime] = useState('');
  const [endTime, setEndTime] = useState('');
  const [selectedCategory, setSelectedCategory] = useState<keyof typeof CATEGORIES>('other');
  const [selectedMood, setSelectedMood] = useState('neutral');
  const [selectedEnergy, setSelectedEnergy] = useState('medium');
  const [productivity, setProductivity] = useState(3);
  const [location, setLocation] = useState('');
  const [notes, setNotes] = useState('');
  
  const colorScheme = useColorScheme();

  const handleSave = () => {
    if (!activity.trim() || !startTime || !endTime) {
      Alert.alert('Missing Information', 'Please fill in activity name, start time, and end time.');
      return;
    }

    // Here you would save to your context/database
    console.log('Saving activity:', {
      activity,
      description,
      startTime,
      endTime,
      category: selectedCategory,
      mood: selectedMood,
      energy: selectedEnergy,
      productivity,
      location,
      notes,
    });

    Alert.alert('Success', 'Activity added successfully!', [
      { text: 'OK', onPress: () => router.back() }
    ]);
  };

  const renderCategorySelector = () => (
    <View style={styles.section}>
      <ThemedText style={styles.sectionTitle}>Category</ThemedText>
      <View style={styles.categoryGrid}>
        {Object.entries(CATEGORIES).map(([key, category]) => (
          <Pressable
            key={key}
            style={[
              styles.categoryItem,
              selectedCategory === key && styles.categoryItemSelected
            ]}
            onPress={() => setSelectedCategory(key as keyof typeof CATEGORIES)}
          >
            <LinearGradient
              colors={selectedCategory === key ? category.gradient : ['transparent', 'transparent']}
              style={styles.categoryGradient}
            >
              <ThemedText style={styles.categoryIcon}>{category.icon}</ThemedText>
              <ThemedText style={[
                styles.categoryLabel,
                { color: selectedCategory === key ? '#fff' : undefined }
              ]}>
                {category.label}
              </ThemedText>
            </LinearGradient>
          </Pressable>
        ))}
      </View>
    </View>
  );

  const renderMoodSelector = () => (
    <View style={styles.section}>
      <ThemedText style={styles.sectionTitle}>How did you feel?</ThemedText>
      <View style={styles.moodContainer}>
        {MOODS.map((mood) => (
          <Pressable
            key={mood.value}
            style={[
              styles.moodItem,
              selectedMood === mood.value && { backgroundColor: mood.color + '20' }
            ]}
            onPress={() => setSelectedMood(mood.value)}
          >
            <ThemedText style={styles.moodEmoji}>{mood.emoji}</ThemedText>
            <ThemedText style={[
              styles.moodLabel,
              selectedMood === mood.value && { color: mood.color, fontWeight: 'bold' }
            ]}>
              {mood.label}
            </ThemedText>
          </Pressable>
        ))}
      </View>
    </View>
  );

  const renderEnergySelector = () => (
    <View style={styles.section}>
      <ThemedText style={styles.sectionTitle}>Energy Level</ThemedText>
      <View style={styles.energyContainer}>
        {ENERGY_LEVELS.map((energy) => (
          <Pressable
            key={energy.value}
            style={[
              styles.energyItem,
              selectedEnergy === energy.value && { backgroundColor: energy.color + '20' }
            ]}
            onPress={() => setSelectedEnergy(energy.value)}
          >
            <View style={styles.energyBars}>
              {[1, 2, 3].map((bar) => (
                <View
                  key={bar}
                  style={[
                    styles.energyBar,
                    {
                      backgroundColor: bar <= energy.bars && selectedEnergy === energy.value
                        ? energy.color
                        : '#E5E7EB'
                    }
                  ]}
                />
              ))}
            </View>
            <ThemedText style={[
              styles.energyLabel,
              selectedEnergy === energy.value && { color: energy.color, fontWeight: 'bold' }
            ]}>
              {energy.label}
            </ThemedText>
          </Pressable>
        ))}
      </View>
    </View>
  );

  const renderProductivitySelector = () => (
    <View style={styles.section}>
      <ThemedText style={styles.sectionTitle}>Productivity (1-5)</ThemedText>
      <View style={styles.productivityContainer}>
        {[1, 2, 3, 4, 5].map((rating) => (
          <Pressable
            key={rating}
            style={[
              styles.productivityItem,
              productivity >= rating && styles.productivityItemSelected
            ]}
            onPress={() => setProductivity(rating)}
          >
            <ThemedText style={[
              styles.productivityStar,
              productivity >= rating && styles.productivityStarSelected
            ]}>
              ⭐
            </ThemedText>
          </Pressable>
        ))}
      </View>
    </View>
  );

  return (
    <GradientBackground variant="ios">
      <StatusBar style={colorScheme === 'dark' ? 'light' : 'dark'} />
      
      {/* Header */}
      <View style={styles.header}>
        <View style={styles.headerTop}>
          <Pressable onPress={() => router.back()} style={styles.backButton}>
            <ThemedText style={styles.backButtonText}>←</ThemedText>
          </Pressable>
          <ThemedText style={styles.headerTitle}>Add Activity</ThemedText>
          <Pressable onPress={handleSave} style={styles.saveButton}>
            <LinearGradient colors={['#667eea', '#764ba2']} style={styles.saveGradient}>
              <ThemedText style={styles.saveButtonText}>Save</ThemedText>
            </LinearGradient>
          </Pressable>
        </View>
      </View>

      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        {/* Basic Info */}
        <View style={styles.section}>
          <Card variant="ios" style={styles.inputCard}>
            <ThemedText style={styles.inputLabel}>Activity Name</ThemedText>
            <TextInput
              style={styles.textInput}
              placeholder="What did you do?"
              placeholderTextColor="#8E8E93"
              value={activity}
              onChangeText={setActivity}
            />
          </Card>
        </View>

        {/* Time */}
        <View style={styles.section}>
          <View style={styles.timeRow}>
            <Card variant="ios" style={[styles.inputCard, styles.timeCard]}>
              <ThemedText style={styles.inputLabel}>Start Time</ThemedText>
              <TextInput
                style={styles.textInput}
                placeholder="09:00"
                placeholderTextColor="#8E8E93"
                value={startTime}
                onChangeText={setStartTime}
              />
            </Card>
            <Card variant="ios" style={[styles.inputCard, styles.timeCard]}>
              <ThemedText style={styles.inputLabel}>End Time</ThemedText>
              <TextInput
                style={styles.textInput}
                placeholder="10:00"
                placeholderTextColor="#8E8E93"
                value={endTime}
                onChangeText={setEndTime}
              />
            </Card>
          </View>
        </View>

        {renderCategorySelector()}
        {renderMoodSelector()}
        {renderEnergySelector()}
        {renderProductivitySelector()}

        {/* Description */}
        <View style={styles.section}>
          <Card variant="ios" style={styles.inputCard}>
            <ThemedText style={styles.inputLabel}>Description</ThemedText>
            <TextInput
              style={[styles.textInput, styles.textArea]}
              placeholder="Tell us more about this activity..."
              placeholderTextColor="#8E8E93"
              value={description}
              onChangeText={setDescription}
              multiline
              numberOfLines={3}
            />
          </Card>
        </View>

        {/* Location */}
        <View style={styles.section}>
          <Card variant="ios" style={styles.inputCard}>
            <ThemedText style={styles.inputLabel}>Location (Optional)</ThemedText>
            <TextInput
              style={styles.textInput}
              placeholder="Where did this happen?"
              placeholderTextColor="#8E8E93"
              value={location}
              onChangeText={setLocation}
            />
          </Card>
        </View>

        {/* Notes */}
        <View style={[styles.section, styles.lastSection]}>
          <Card variant="ios" style={styles.inputCard}>
            <ThemedText style={styles.inputLabel}>Notes (Optional)</ThemedText>
            <TextInput
              style={[styles.textInput, styles.textArea]}
              placeholder="Any additional thoughts or reflections..."
              placeholderTextColor="#8E8E93"
              value={notes}
              onChangeText={setNotes}
              multiline
              numberOfLines={2}
            />
          </Card>
        </View>
      </ScrollView>
    </GradientBackground>
  );
}

const styles = StyleSheet.create({
  header: {
    paddingTop: 60,
    paddingHorizontal: 20,
    paddingBottom: 20,
  },
  headerTop: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  backButton: {
    width: 44,
    height: 44,
    borderRadius: 22,
    backgroundColor: 'rgba(102, 126, 234, 0.1)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  backButtonText: {
    fontSize: 24,
    fontWeight: 'bold',
  },
  headerTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    flex: 1,
    textAlign: 'center',
  },
  saveButton: {
    borderRadius: 22,
    overflow: 'hidden',
  },
  saveGradient: {
    paddingHorizontal: 20,
    paddingVertical: 12,
  },
  saveButtonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: 'bold',
  },
  content: {
    flex: 1,
    paddingHorizontal: 20,
  },
  section: {
    marginBottom: 24,
  },
  lastSection: {
    marginBottom: 100,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 12,
  },
  inputCard: {
    paddingVertical: 16,
    paddingHorizontal: 20,
  },
  inputLabel: {
    fontSize: 14,
    fontWeight: '600',
    marginBottom: 8,
    opacity: 0.8,
  },
  textInput: {
    fontSize: 16,
    paddingVertical: 8,
    color: '#1D1D1F',
  },
  textArea: {
    minHeight: 60,
    textAlignVertical: 'top',
  },
  timeRow: {
    flexDirection: 'row',
    gap: 12,
  },
  timeCard: {
    flex: 1,
  },
  categoryGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 12,
  },
  categoryItem: {
    width: '30%',
    borderRadius: 16,
    overflow: 'hidden',
  },
  categoryItemSelected: {
    shadowColor: '#667eea',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.3,
    shadowRadius: 8,
    elevation: 8,
  },
  categoryGradient: {
    padding: 16,
    alignItems: 'center',
    backgroundColor: 'rgba(255, 255, 255, 0.8)',
  },
  categoryIcon: {
    fontSize: 24,
    marginBottom: 8,
  },
  categoryLabel: {
    fontSize: 12,
    fontWeight: '600',
    textAlign: 'center',
  },
  moodContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  moodItem: {
    flex: 1,
    alignItems: 'center',
    paddingVertical: 16,
    paddingHorizontal: 8,
    borderRadius: 12,
    marginHorizontal: 2,
  },
  moodEmoji: {
    fontSize: 24,
    marginBottom: 8,
  },
  moodLabel: {
    fontSize: 12,
    fontWeight: '500',
    textAlign: 'center',
  },
  energyContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  energyItem: {
    flex: 1,
    alignItems: 'center',
    paddingVertical: 16,
    paddingHorizontal: 8,
    borderRadius: 12,
    marginHorizontal: 2,
  },
  energyBars: {
    flexDirection: 'row',
    gap: 2,
    marginBottom: 8,
  },
  energyBar: {
    width: 6,
    height: 16,
    borderRadius: 3,
  },
  energyLabel: {
    fontSize: 12,
    fontWeight: '500',
    textAlign: 'center',
  },
  productivityContainer: {
    flexDirection: 'row',
    justifyContent: 'center',
    gap: 8,
  },
  productivityItem: {
    padding: 8,
    borderRadius: 8,
  },
  productivityItemSelected: {
    backgroundColor: 'rgba(102, 126, 234, 0.1)',
  },
  productivityStar: {
    fontSize: 32,
    opacity: 0.3,
  },
  productivityStarSelected: {
    opacity: 1,
  },
});

import { LinearGradient } from 'expo-linear-gradient';
import { router } from 'expo-router';
import { StatusBar } from 'expo-status-bar';
import React, { useState } from 'react';
import { Alert, Pressable, ScrollView, StyleSheet, TextInput, View } from 'react-native';

import { GradientBackground } from '@/components/GradientBackground';
import { ThemedText } from '@/components/ThemedText';
import { useColorScheme } from '@/hooks/useColorScheme';

const CATEGORIES = {
  work: { color: '#3B82F6', icon: '💼', label: 'Work', gradient: ['#3B82F6', '#1D4ED8'] },
  personal: { color: '#10B981', icon: '🏠', label: 'Personal', gradient: ['#10B981', '#059669'] },
  health: { color: '#F59E0B', icon: '🏃‍♂️', label: 'Health', gradient: ['#F59E0B', '#D97706'] },
  social: { color: '#EF4444', icon: '👥', label: 'Social', gradient: ['#EF4444', '#DC2626'] },
  entertainment: { color: '#8B5CF6', icon: '🎬', label: 'Entertainment', gradient: ['#8B5CF6', '#7C3AED'] },
  travel: { color: '#06B6D4', icon: '✈️', label: 'Travel', gradient: ['#06B6D4', '#0891B2'] },
  food: { color: '#F97316', icon: '🍽️', label: 'Food', gradient: ['#F97316', '#EA580C'] },
  exercise: { color: '#84CC16', icon: '💪', label: 'Exercise', gradient: ['#84CC16', '#65A30D'] },
  sleep: { color: '#6366F1', icon: '😴', label: 'Sleep', gradient: ['#6366F1', '#4F46E5'] },
  other: { color: '#6B7280', icon: '📝', label: 'Other', gradient: ['#6B7280', '#4B5563'] },
};

const MOODS = [
  { value: 'excellent', emoji: '😄', label: 'Excellent', color: '#10B981' },
  { value: 'good', emoji: '😊', label: 'Good', color: '#84CC16' },
  { value: 'neutral', emoji: '😐', label: 'Neutral', color: '#F59E0B' },
  { value: 'bad', emoji: '😔', label: 'Bad', color: '#EF4444' },
  { value: 'terrible', emoji: '😢', label: 'Terrible', color: '#DC2626' },
];

const ENERGY_LEVELS = [
  { value: 'high', label: 'High', color: '#10B981', bars: 3 },
  { value: 'medium', label: 'Medium', color: '#F59E0B', bars: 2 },
  { value: 'low', label: 'Low', color: '#EF4444', bars: 1 },
];

export default function AddActivityScreen() {
  const [activity, setActivity] = useState('');
  const [time, setTime] = useState('');
  const [description, setDescription] = useState('');
  const [selectedMood, setSelectedMood] = useState('good');
  const [hasVoiceNote, setHasVoiceNote] = useState(false);
  const [hasPhoto, setHasPhoto] = useState(false);
  const [isRecording, setIsRecording] = useState(false);

  const colorScheme = useColorScheme();

  const handleSave = () => {
    if (!activity.trim() || !time) {
      Alert.alert('Missing Information', 'Please fill in activity name and time.');
      return;
    }

    // Here you would save to your context/database
    console.log('Saving activity:', {
      activity,
      time,
      description,
      mood: selectedMood,
      hasVoiceNote,
      hasPhoto,
    });

    Alert.alert('Success', 'Activity added successfully!', [
      { text: 'OK', onPress: () => router.back() }
    ]);
  };

  const handleVoiceRecord = () => {
    if (isRecording) {
      setIsRecording(false);
      setHasVoiceNote(true);
      Alert.alert('Voice Note', 'Voice note saved successfully!');
    } else {
      setIsRecording(true);
      Alert.alert('Recording', 'Voice recording started...');
    }
  };

  const handleTakePhoto = () => {
    setHasPhoto(true);
    Alert.alert('Photo', 'Photo captured successfully!');
  };

  const getMoodEmoji = (mood: string) => {
    switch (mood) {
      case 'excellent': return '😄';
      case 'good': return '😊';
      case 'neutral': return '😐';
      case 'bad': return '😔';
      case 'terrible': return '😢';
      default: return '😊';
    }
  };

  const renderCategorySelector = () => (
    <View style={styles.section}>
      <ThemedText style={styles.sectionTitle}>Category</ThemedText>
      <View style={styles.categoryGrid}>
        {Object.entries(CATEGORIES).map(([key, category]) => (
          <Pressable
            key={key}
            style={[
              styles.categoryItem,
              selectedCategory === key && styles.categoryItemSelected
            ]}
            onPress={() => setSelectedCategory(key as keyof typeof CATEGORIES)}
          >
            <LinearGradient
              colors={selectedCategory === key ? category.gradient : ['transparent', 'transparent']}
              style={styles.categoryGradient}
            >
              <ThemedText style={styles.categoryIcon}>{category.icon}</ThemedText>
              <ThemedText style={[
                styles.categoryLabel,
                { color: selectedCategory === key ? '#fff' : undefined }
              ]}>
                {category.label}
              </ThemedText>
            </LinearGradient>
          </Pressable>
        ))}
      </View>
    </View>
  );

  const renderMoodSelector = () => (
    <View style={styles.section}>
      <ThemedText style={styles.sectionTitle}>How did you feel?</ThemedText>
      <View style={styles.moodContainer}>
        {MOODS.map((mood) => (
          <Pressable
            key={mood.value}
            style={[
              styles.moodItem,
              selectedMood === mood.value && { backgroundColor: mood.color + '20' }
            ]}
            onPress={() => setSelectedMood(mood.value)}
          >
            <ThemedText style={styles.moodEmoji}>{mood.emoji}</ThemedText>
            <ThemedText style={[
              styles.moodLabel,
              selectedMood === mood.value && { color: mood.color, fontWeight: 'bold' }
            ]}>
              {mood.label}
            </ThemedText>
          </Pressable>
        ))}
      </View>
    </View>
  );

  const renderEnergySelector = () => (
    <View style={styles.section}>
      <ThemedText style={styles.sectionTitle}>Energy Level</ThemedText>
      <View style={styles.energyContainer}>
        {ENERGY_LEVELS.map((energy) => (
          <Pressable
            key={energy.value}
            style={[
              styles.energyItem,
              selectedEnergy === energy.value && { backgroundColor: energy.color + '20' }
            ]}
            onPress={() => setSelectedEnergy(energy.value)}
          >
            <View style={styles.energyBars}>
              {[1, 2, 3].map((bar) => (
                <View
                  key={bar}
                  style={[
                    styles.energyBar,
                    {
                      backgroundColor: bar <= energy.bars && selectedEnergy === energy.value
                        ? energy.color
                        : '#E5E7EB'
                    }
                  ]}
                />
              ))}
            </View>
            <ThemedText style={[
              styles.energyLabel,
              selectedEnergy === energy.value && { color: energy.color, fontWeight: 'bold' }
            ]}>
              {energy.label}
            </ThemedText>
          </Pressable>
        ))}
      </View>
    </View>
  );

  const renderProductivitySelector = () => (
    <View style={styles.section}>
      <ThemedText style={styles.sectionTitle}>Productivity (1-5)</ThemedText>
      <View style={styles.productivityContainer}>
        {[1, 2, 3, 4, 5].map((rating) => (
          <Pressable
            key={rating}
            style={[
              styles.productivityItem,
              productivity >= rating && styles.productivityItemSelected
            ]}
            onPress={() => setProductivity(rating)}
          >
            <ThemedText style={[
              styles.productivityStar,
              productivity >= rating && styles.productivityStarSelected
            ]}>
              ⭐
            </ThemedText>
          </Pressable>
        ))}
      </View>
    </View>
  );

  return (
    <GradientBackground variant="primary">
      <StatusBar style="dark" />

      {/* Modern Header */}
      <View style={styles.header}>
        <View style={styles.headerTop}>
          <Pressable onPress={() => router.back()} style={styles.backButton}>
            <View style={styles.backContainer}>
              <ThemedText style={styles.backIcon}>←</ThemedText>
            </View>
          </Pressable>

          <View style={styles.headerCenter}>
            <ThemedText style={styles.headerTitle}>Add Activity</ThemedText>
            <ThemedText style={styles.headerSubtitle}>Log what you did</ThemedText>
          </View>

          <Pressable onPress={handleSave} style={styles.saveButton}>
            <LinearGradient
              colors={['#6366F1', '#8B5CF6']}
              style={styles.saveGradient}
              start={{ x: 0, y: 0 }}
              end={{ x: 1, y: 1 }}
            >
              <ThemedText style={styles.saveText}>Save</ThemedText>
            </LinearGradient>
          </Pressable>
        </View>
      </View>

      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        {/* Activity Input */}
        <View style={styles.section}>
          <View style={styles.inputCard}>
            <ThemedText style={styles.inputLabel}>What did you do?</ThemedText>
            <TextInput
              style={styles.textInput}
              placeholder="e.g., Had breakfast, Went for a walk..."
              placeholderTextColor="#94A3B8"
              value={activity}
              onChangeText={setActivity}
            />
          </View>
        </View>

        {/* Time Input */}
        <View style={styles.section}>
          <View style={styles.inputCard}>
            <ThemedText style={styles.inputLabel}>What time?</ThemedText>
            <TextInput
              style={styles.textInput}
              placeholder="e.g., 08:00 AM"
              placeholderTextColor="#94A3B8"
              value={time}
              onChangeText={setTime}
            />
          </View>
        </View>

        {/* Media Section */}
        <View style={styles.section}>
          <View style={styles.mediaContainer}>
            <ThemedText style={styles.sectionTitle}>Add Media</ThemedText>

            <View style={styles.mediaRow}>
              {/* Voice Note */}
              <Pressable
                style={[styles.mediaButton, hasVoiceNote && styles.mediaButtonActive]}
                onPress={handleVoiceRecord}
              >
                <View style={styles.mediaIconContainer}>
                  <ThemedText style={styles.mediaIcon}>
                    {isRecording ? '⏹️' : '🎤'}
                  </ThemedText>
                  {hasVoiceNote && (
                    <View style={styles.activeIndicator}>
                      <ThemedText style={styles.activeIndicatorText}>✓</ThemedText>
                    </View>
                  )}
                </View>
                <ThemedText style={styles.mediaLabel}>
                  {isRecording ? 'Recording...' : hasVoiceNote ? 'Voice Added' : 'Voice Note'}
                </ThemedText>
              </Pressable>

              {/* Photo */}
              <Pressable
                style={[styles.mediaButton, hasPhoto && styles.mediaButtonActive]}
                onPress={handleTakePhoto}
              >
                <View style={styles.mediaIconContainer}>
                  <ThemedText style={styles.mediaIcon}>📷</ThemedText>
                  {hasPhoto && (
                    <View style={styles.activeIndicator}>
                      <ThemedText style={styles.activeIndicatorText}>✓</ThemedText>
                    </View>
                  )}
                </View>
                <ThemedText style={styles.mediaLabel}>
                  {hasPhoto ? 'Photo Added' : 'Take Photo'}
                </ThemedText>
              </Pressable>
            </View>
          </View>
        </View>

        {/* Mood Selector */}
        <View style={styles.section}>
          <View style={styles.moodContainer}>
            <ThemedText style={styles.sectionTitle}>How did you feel?</ThemedText>
            <View style={styles.moodGrid}>
              {MOODS.map((mood) => (
                <Pressable
                  key={mood.value}
                  style={[
                    styles.moodButton,
                    selectedMood === mood.value && styles.moodButtonActive,
                  ]}
                  onPress={() => setSelectedMood(mood.value)}
                >
                  <ThemedText style={styles.moodEmoji}>{getMoodEmoji(mood.value)}</ThemedText>
                  <ThemedText style={[
                    styles.moodText,
                    selectedMood === mood.value && styles.moodTextActive,
                  ]}>
                    {mood.label}
                  </ThemedText>
                </Pressable>
              ))}
            </View>
          </View>
        </View>

        {/* Description */}
        <View style={[styles.section, styles.lastSection]}>
          <View style={styles.inputCard}>
            <ThemedText style={styles.inputLabel}>Description (Optional)</ThemedText>
            <TextInput
              style={[styles.textInput, styles.textArea]}
              placeholder="Tell us more about this activity..."
              placeholderTextColor="#94A3B8"
              value={description}
              onChangeText={setDescription}
              multiline
              numberOfLines={4}
            />
          </View>
        </View>
      </ScrollView>
    </GradientBackground>
  );
}

const styles = StyleSheet.create({
  // Modern Header Styles
  header: {
    paddingTop: 60,
    paddingHorizontal: 24,
    paddingBottom: 32,
  },
  headerTop: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  backButton: {
    width: 48,
    height: 48,
  },
  backContainer: {
    width: 48,
    height: 48,
    borderRadius: 24,
    backgroundColor: '#FFFFFF',
    justifyContent: 'center',
    alignItems: 'center',
    shadowColor: '#64748B',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 4,
  },
  backIcon: {
    fontSize: 20,
    fontWeight: '600',
    color: '#475569',
  },
  headerCenter: {
    flex: 1,
    alignItems: 'center',
  },
  headerTitle: {
    fontSize: 24,
    fontWeight: '700',
    color: '#1E293B',
    letterSpacing: -0.5,
  },
  headerSubtitle: {
    fontSize: 14,
    fontWeight: '500',
    color: '#64748B',
    marginTop: 2,
  },
  saveButton: {
    borderRadius: 24,
    overflow: 'hidden',
    shadowColor: '#6366F1',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.2,
    shadowRadius: 12,
    elevation: 8,
  },
  saveGradient: {
    paddingHorizontal: 20,
    paddingVertical: 12,
  },
  saveText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: '700',
  },
  content: {
    flex: 1,
    paddingHorizontal: 24,
  },
  section: {
    marginBottom: 24,
  },
  lastSection: {
    marginBottom: 100,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '700',
    color: '#1E293B',
    marginBottom: 16,
  },
  inputCard: {
    backgroundColor: '#FFFFFF',
    borderRadius: 20,
    paddingVertical: 24,
    paddingHorizontal: 24,
    shadowColor: '#64748B',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.08,
    shadowRadius: 16,
    elevation: 8,
  },
  inputLabel: {
    fontSize: 16,
    fontWeight: '600',
    color: '#374151',
    marginBottom: 12,
  },
  textInput: {
    fontSize: 16,
    color: '#1F2937',
    paddingVertical: 16,
    paddingHorizontal: 16,
    backgroundColor: '#F8FAFC',
    borderRadius: 12,
    borderWidth: 1,
    borderColor: '#E2E8F0',
  },
  textArea: {
    height: 100,
    textAlignVertical: 'top',
  },
  timeRow: {
    flexDirection: 'row',
    gap: 12,
  },
  timeCard: {
    flex: 1,
  },
  categoryGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 12,
  },
  categoryItem: {
    width: '30%',
    borderRadius: 16,
    overflow: 'hidden',
  },
  categoryItemSelected: {
    shadowColor: '#667eea',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.3,
    shadowRadius: 8,
    elevation: 8,
  },
  categoryGradient: {
    padding: 16,
    alignItems: 'center',
    backgroundColor: 'rgba(255, 255, 255, 0.8)',
  },
  categoryIcon: {
    fontSize: 24,
    marginBottom: 8,
  },
  categoryLabel: {
    fontSize: 12,
    fontWeight: '600',
    textAlign: 'center',
  },
  moodContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  moodItem: {
    flex: 1,
    alignItems: 'center',
    paddingVertical: 16,
    paddingHorizontal: 8,
    borderRadius: 12,
    marginHorizontal: 2,
  },
  moodEmoji: {
    fontSize: 24,
    marginBottom: 8,
  },
  moodLabel: {
    fontSize: 12,
    fontWeight: '500',
    textAlign: 'center',
  },
  energyContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  energyItem: {
    flex: 1,
    alignItems: 'center',
    paddingVertical: 16,
    paddingHorizontal: 8,
    borderRadius: 12,
    marginHorizontal: 2,
  },
  energyBars: {
    flexDirection: 'row',
    gap: 2,
    marginBottom: 8,
  },
  energyBar: {
    width: 6,
    height: 16,
    borderRadius: 3,
  },
  energyLabel: {
    fontSize: 12,
    fontWeight: '500',
    textAlign: 'center',
  },
  productivityContainer: {
    flexDirection: 'row',
    justifyContent: 'center',
    gap: 8,
  },
  productivityItem: {
    padding: 8,
    borderRadius: 8,
  },
  productivityItemSelected: {
    backgroundColor: 'rgba(102, 126, 234, 0.1)',
  },
  productivityStar: {
    fontSize: 32,
    opacity: 0.3,
  },
  productivityStarSelected: {
    opacity: 1,
  },
  // Media styles
  mediaCard: {
    paddingVertical: 20,
    paddingHorizontal: 20,
  },
  mediaHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  mediaTitle: {
    fontSize: 18,
    fontWeight: 'bold',
  },
  mediaIndicator: {
    backgroundColor: '#10B981',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 8,
  },
  mediaIndicatorText: {
    color: '#FFFFFF',
    fontSize: 12,
    fontWeight: '600',
  },
  voiceButton: {
    borderRadius: 16,
    overflow: 'hidden',
  },
  voiceGradient: {
    paddingVertical: 16,
    paddingHorizontal: 20,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    gap: 12,
  },
  voiceIcon: {
    fontSize: 24,
  },
  voiceText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: '600',
  },
  photoButton: {
    borderRadius: 16,
    overflow: 'hidden',
  },
  photoGradient: {
    paddingVertical: 16,
    paddingHorizontal: 20,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    gap: 12,
  },
  photoIcon: {
    fontSize: 24,
  },
  photoText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: '600',
  },
  // Mood styles
  moodGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 12,
  },
  moodItem: {
    flex: 1,
    minWidth: '30%',
    borderRadius: 12,
    overflow: 'hidden',
    borderWidth: 2,
    borderColor: 'transparent',
  },
  selectedMoodItem: {
    borderColor: '#667eea',
  },
  moodGradient: {
    paddingVertical: 16,
    paddingHorizontal: 12,
    alignItems: 'center',
    backgroundColor: 'rgba(255, 255, 255, 0.8)',
  },
  moodEmoji: {
    fontSize: 24,
    marginBottom: 8,
  },
  moodText: {
    fontSize: 14,
    fontWeight: '600',
    textAlign: 'center',
  },
  // Modern Media Styles
  mediaContainer: {
    backgroundColor: '#FFFFFF',
    borderRadius: 20,
    paddingVertical: 24,
    paddingHorizontal: 24,
    shadowColor: '#64748B',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.08,
    shadowRadius: 16,
    elevation: 8,
  },
  mediaRow: {
    flexDirection: 'row',
    gap: 16,
  },
  mediaButton: {
    flex: 1,
    backgroundColor: '#F8FAFC',
    borderRadius: 16,
    paddingVertical: 20,
    paddingHorizontal: 16,
    alignItems: 'center',
    borderWidth: 2,
    borderColor: '#E2E8F0',
  },
  mediaButtonActive: {
    backgroundColor: '#EEF4FF',
    borderColor: '#6366F1',
  },
  mediaIconContainer: {
    position: 'relative',
    marginBottom: 8,
  },
  mediaIcon: {
    fontSize: 32,
  },
  activeIndicator: {
    position: 'absolute',
    top: -4,
    right: -4,
    width: 20,
    height: 20,
    borderRadius: 10,
    backgroundColor: '#10B981',
    justifyContent: 'center',
    alignItems: 'center',
  },
  activeIndicatorText: {
    fontSize: 12,
    color: '#FFFFFF',
    fontWeight: '700',
  },
  mediaLabel: {
    fontSize: 14,
    fontWeight: '600',
    color: '#64748B',
    textAlign: 'center',
  },
  // Modern Mood Styles
  moodContainer: {
    backgroundColor: '#FFFFFF',
    borderRadius: 20,
    paddingVertical: 24,
    paddingHorizontal: 24,
    shadowColor: '#64748B',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.08,
    shadowRadius: 16,
    elevation: 8,
  },
  moodGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 12,
  },
  moodButton: {
    flex: 1,
    minWidth: '30%',
    backgroundColor: '#F8FAFC',
    borderRadius: 16,
    paddingVertical: 16,
    paddingHorizontal: 12,
    alignItems: 'center',
    borderWidth: 2,
    borderColor: '#E2E8F0',
  },
  moodButtonActive: {
    backgroundColor: '#EEF4FF',
    borderColor: '#6366F1',
  },
  moodEmoji: {
    fontSize: 28,
    marginBottom: 8,
  },
  moodText: {
    fontSize: 14,
    fontWeight: '600',
    color: '#64748B',
    textAlign: 'center',
  },
  moodTextActive: {
    color: '#6366F1',
    fontWeight: '700',
  },
});

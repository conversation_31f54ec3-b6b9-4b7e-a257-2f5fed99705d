import React, { useState } from 'react';
import { Modal, View, StyleSheet, TextInput, Alert, Switch } from 'react-native';
import { ThemedText } from './ThemedText';
import { ThemedView } from './ThemedView';
import { Button } from './Button';
import { Card } from './Card';
import * as Sharing from 'expo-sharing';
import * as FileSystem from 'expo-file-system';
import { JournalEntry } from '@/context/JournalContext';

interface ShareModalProps {
  visible: boolean;
  onClose: () => void;
  entry: JournalEntry;
}

export function ShareModal({ visible, onClose, entry }: ShareModalProps) {
  const [password, setPassword] = useState('');
  const [isPasswordProtected, setIsPasswordProtected] = useState(false);
  const [isLoading, setIsLoading] = useState(false);

  const handleShare = async () => {
    setIsLoading(true);
    try {
      // Create shareable content
      let content = `📖 Journal Entry - ${entry.title}\n\n`;
      content += `📅 Date: ${entry.date}\n`;
      if (entry.mood) {
        content += `😊 Mood: ${entry.mood}\n`;
      }
      content += `\n${entry.content}\n\n`;
      
      if (entry.tags && entry.tags.length > 0) {
        content += `🏷️ Tags: ${entry.tags.join(', ')}\n`;
      }
      
      content += `\n📊 Stats: ${entry.wordCount} words • ${entry.readingTime} min read\n`;
      content += `\n✨ Shared from My Daily Journal App`;

      if (isPasswordProtected && password.trim()) {
        content += `\n\n🔒 This entry is password protected.\nPassword: ${password.trim()}`;
      }

      // Create temporary file
      const fileName = `journal-entry-${entry.id}.txt`;
      const fileUri = FileSystem.documentDirectory + fileName;
      
      await FileSystem.writeAsStringAsync(fileUri, content);
      
      // Share the file
      if (await Sharing.isAvailableAsync()) {
        await Sharing.shareAsync(fileUri, {
          mimeType: 'text/plain',
          dialogTitle: 'Share Journal Entry',
        });
      } else {
        Alert.alert('Sharing not available', 'Sharing is not available on this device.');
      }
      
      // Clean up temporary file
      await FileSystem.deleteAsync(fileUri, { idempotent: true });
      
      onClose();
    } catch (error) {
      Alert.alert('Error', 'Failed to share entry. Please try again.');
      console.error('Share error:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleClose = () => {
    setPassword('');
    setIsPasswordProtected(false);
    onClose();
  };

  return (
    <Modal
      visible={visible}
      animationType="slide"
      presentationStyle="pageSheet"
      onRequestClose={handleClose}
    >
      <ThemedView style={styles.container}>
        <View style={styles.header}>
          <ThemedText style={styles.title}>Share Entry</ThemedText>
          <Button
            title="Cancel"
            onPress={handleClose}
            variant="ghost"
            size="small"
          />
        </View>

        <Card style={styles.entryPreview}>
          <ThemedText style={styles.entryTitle}>{entry.title}</ThemedText>
          <ThemedText style={styles.entryDate}>{entry.date}</ThemedText>
          <ThemedText numberOfLines={3} style={styles.entryContent}>
            {entry.content}
          </ThemedText>
        </Card>

        <Card style={styles.optionsCard}>
          <View style={styles.option}>
            <View style={styles.optionText}>
              <ThemedText style={styles.optionTitle}>Password Protection</ThemedText>
              <ThemedText style={styles.optionDescription}>
                Add a password to protect this entry when shared
              </ThemedText>
            </View>
            <Switch
              value={isPasswordProtected}
              onValueChange={setIsPasswordProtected}
              trackColor={{ false: '#E5E7EB', true: '#6366F1' }}
              thumbColor={isPasswordProtected ? '#FFFFFF' : '#9CA3AF'}
            />
          </View>

          {isPasswordProtected && (
            <View style={styles.passwordContainer}>
              <ThemedText style={styles.passwordLabel}>Password</ThemedText>
              <TextInput
                style={styles.passwordInput}
                placeholder="Enter password for this entry"
                placeholderTextColor="#9CA3AF"
                value={password}
                onChangeText={setPassword}
                secureTextEntry
                autoCapitalize="none"
              />
            </View>
          )}
        </Card>

        <View style={styles.actions}>
          <Button
            title={isLoading ? "Sharing..." : "Share Entry"}
            onPress={handleShare}
            disabled={isLoading || (isPasswordProtected && !password.trim())}
            variant="primary"
            style={styles.shareButton}
          />
        </View>
      </ThemedView>
    </Modal>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 20,
    paddingTop: 60,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 20,
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
  },
  entryPreview: {
    marginBottom: 20,
  },
  entryTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 8,
  },
  entryDate: {
    fontSize: 14,
    opacity: 0.7,
    marginBottom: 12,
  },
  entryContent: {
    fontSize: 16,
    lineHeight: 22,
    opacity: 0.8,
  },
  optionsCard: {
    marginBottom: 20,
  },
  option: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 8,
  },
  optionText: {
    flex: 1,
    marginRight: 16,
  },
  optionTitle: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 4,
  },
  optionDescription: {
    fontSize: 14,
    opacity: 0.7,
  },
  passwordContainer: {
    marginTop: 16,
    paddingTop: 16,
    borderTopWidth: 1,
    borderTopColor: '#E5E7EB',
  },
  passwordLabel: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 8,
  },
  passwordInput: {
    borderWidth: 1,
    borderColor: '#E5E7EB',
    borderRadius: 8,
    paddingHorizontal: 12,
    paddingVertical: 10,
    fontSize: 16,
    backgroundColor: '#F9FAFB',
  },
  actions: {
    marginTop: 'auto',
    paddingBottom: 20,
  },
  shareButton: {
    width: '100%',
  },
});

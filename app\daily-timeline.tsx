import { LinearGradient } from 'expo-linear-gradient';
import { router, useLocalSearchParams } from 'expo-router';
import { StatusBar } from 'expo-status-bar';
import React, { useState } from 'react';
import { Alert, Dimensions, Pressable, ScrollView, StyleSheet, View } from 'react-native';

import { GradientBackground } from '@/components/GradientBackground';
import { ThemedText } from '@/components/ThemedText';
import { useColorScheme } from '@/hooks/useColorScheme';

const { width } = Dimensions.get('window');

// Sample timeline data
const SAMPLE_TIMELINE = [
  {
    id: '1',
    time: '05:00',
    activity: 'Woke up',
    description: 'Started the day early, feeling refreshed',
    hasVoice: false,
    hasPhoto: false,
    mood: 'good',
  },
  {
    id: '2',
    time: '08:00',
    activity: 'Breakfast',
    description: 'Had a healthy breakfast with fruits and coffee',
    hasVoice: true,
    hasPhoto: true,
    mood: 'excellent',
  },
  {
    id: '3',
    time: '09:30',
    activity: 'Work Session',
    description: 'Deep focus work on the new project',
    hasVoice: false,
    hasPhoto: false,
    mood: 'good',
  },
  {
    id: '4',
    time: '12:00',
    activity: 'Lunch Break',
    description: 'Had lunch with colleagues, great conversations',
    hasVoice: true,
    hasPhoto: true,
    mood: 'excellent',
  },
  {
    id: '5',
    time: '18:00',
    activity: 'Gym Workout',
    description: 'Intense cardio and strength training session',
    hasVoice: false,
    hasPhoto: true,
    mood: 'excellent',
  },
];

export default function DailyTimelineScreen() {
  const { date } = useLocalSearchParams();
  const [timeline, setTimeline] = useState(SAMPLE_TIMELINE);
  const colorScheme = useColorScheme();

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', { 
      weekday: 'long', 
      year: 'numeric', 
      month: 'long', 
      day: 'numeric' 
    });
  };

  const getMoodEmoji = (mood: string) => {
    switch (mood) {
      case 'excellent': return '😄';
      case 'good': return '😊';
      case 'neutral': return '😐';
      case 'bad': return '😔';
      case 'terrible': return '😢';
      default: return '😐';
    }
  };

  const handleShare = () => {
    Alert.alert(
      'Share Day',
      'Share your entire day with friends and family?',
      [
        { text: 'Cancel', style: 'cancel' },
        { text: 'Share', onPress: () => console.log('Sharing day...') }
      ]
    );
  };

  const renderTimelineItem = (item: any, index: number) => {
    return (
      <View key={item.id} style={styles.timelineItem}>
        {/* Modern Time indicator */}
        <View style={styles.timelineLeft}>
          <View style={styles.timeContainer}>
            <ThemedText style={styles.timeText}>{item.time}</ThemedText>
          </View>
          <View style={styles.timelineDot} />
          {index < timeline.length - 1 && <View style={styles.timelineLine} />}
        </View>

        {/* Modern Activity card */}
        <View style={styles.activityCard}>
          <View style={styles.activityHeader}>
            <ThemedText style={styles.activityTitle}>{item.activity}</ThemedText>
            <View style={styles.activityMeta}>
              {item.hasVoice && (
                <View style={styles.metaIcon}>
                  <ThemedText style={styles.metaEmoji}>🎤</ThemedText>
                </View>
              )}
              {item.hasPhoto && (
                <View style={styles.metaIcon}>
                  <ThemedText style={styles.metaEmoji}>📷</ThemedText>
                </View>
              )}
              <View style={styles.moodContainer}>
                <ThemedText style={styles.moodEmoji}>
                  {getMoodEmoji(item.mood)}
                </ThemedText>
              </View>
            </View>
          </View>

          <ThemedText style={styles.activityDescription}>
            {item.description}
          </ThemedText>

          <View style={styles.activityActions}>
            <Pressable style={styles.editButton}>
              <ThemedText style={styles.editButtonText}>Edit</ThemedText>
            </Pressable>
            {(item.hasVoice || item.hasPhoto) && (
              <Pressable style={styles.viewMediaButton}>
                <LinearGradient
                  colors={['#6366F1', '#8B5CF6']}
                  style={styles.viewMediaGradient}
                  start={{ x: 0, y: 0 }}
                  end={{ x: 1, y: 1 }}
                >
                  <ThemedText style={styles.viewMediaText}>View Media</ThemedText>
                </LinearGradient>
              </Pressable>
            )}
          </View>
        </View>
      </View>
    );
  };

  return (
    <GradientBackground variant="primary">
      <StatusBar style="dark" />

      {/* Modern Header */}
      <View style={styles.header}>
        <View style={styles.headerTop}>
          <Pressable onPress={() => router.back()} style={styles.backButton}>
            <View style={styles.backContainer}>
              <ThemedText style={styles.backIcon}>←</ThemedText>
            </View>
          </Pressable>

          <View style={styles.headerCenter}>
            <ThemedText style={styles.headerTitle}>Daily Timeline</ThemedText>
            <ThemedText style={styles.headerDate}>
              {date ? formatDate(date as string) : 'Today'}
            </ThemedText>
          </View>

          <Pressable onPress={handleShare} style={styles.shareButton}>
            <LinearGradient
              colors={['#6366F1', '#8B5CF6']}
              style={styles.shareGradient}
              start={{ x: 0, y: 0 }}
              end={{ x: 1, y: 1 }}
            >
              <ThemedText style={styles.shareIcon}>📤</ThemedText>
            </LinearGradient>
          </Pressable>
        </View>

        {/* Modern Day Summary */}
        <View style={styles.summaryCard}>
          <View style={styles.summaryContent}>
            <View style={styles.summaryItem}>
              <ThemedText style={styles.summaryNumber}>{timeline.length}</ThemedText>
              <ThemedText style={styles.summaryLabel}>Activities</ThemedText>
            </View>
            <View style={styles.summaryItem}>
              <ThemedText style={styles.summaryNumber}>
                {timeline.filter(item => item.hasVoice).length}
              </ThemedText>
              <ThemedText style={styles.summaryLabel}>Voice Notes</ThemedText>
            </View>
            <View style={styles.summaryItem}>
              <ThemedText style={styles.summaryNumber}>
                {timeline.filter(item => item.hasPhoto).length}
              </ThemedText>
              <ThemedText style={styles.summaryLabel}>Photos</ThemedText>
            </View>
          </View>
        </View>
      </View>

      {/* Timeline */}
      <ScrollView 
        style={styles.timelineContainer}
        showsVerticalScrollIndicator={false}
        contentContainerStyle={styles.timelineContent}
      >
        {timeline.map((item, index) => renderTimelineItem(item, index))}
        
        {/* Modern Add Activity Button */}
        <View style={styles.addActivityContainer}>
          <Pressable
            style={styles.addActivityButton}
            onPress={() => router.push('/add-activity')}
          >
            <LinearGradient
              colors={['#6366F1', '#8B5CF6']}
              style={styles.addActivityGradient}
              start={{ x: 0, y: 0 }}
              end={{ x: 1, y: 1 }}
            >
              <ThemedText style={styles.addActivityIcon}>+</ThemedText>
              <ThemedText style={styles.addActivityText}>Add Activity</ThemedText>
            </LinearGradient>
          </Pressable>
        </View>
      </ScrollView>
    </GradientBackground>
  );
}

const styles = StyleSheet.create({
  // Modern Header Styles
  header: {
    paddingTop: 60,
    paddingHorizontal: 24,
    paddingBottom: 32,
  },
  headerTop: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 24,
  },
  backButton: {
    width: 48,
    height: 48,
  },
  backContainer: {
    width: 48,
    height: 48,
    borderRadius: 24,
    backgroundColor: '#FFFFFF',
    justifyContent: 'center',
    alignItems: 'center',
    shadowColor: '#64748B',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 4,
  },
  backIcon: {
    fontSize: 20,
    fontWeight: '600',
    color: '#475569',
  },
  headerCenter: {
    flex: 1,
    alignItems: 'center',
  },
  headerTitle: {
    fontSize: 24,
    fontWeight: '700',
    color: '#1E293B',
    marginBottom: 4,
    letterSpacing: -0.5,
  },
  headerDate: {
    fontSize: 14,
    fontWeight: '500',
    color: '#64748B',
  },
  shareButton: {
    width: 48,
    height: 48,
    borderRadius: 24,
    overflow: 'hidden',
    shadowColor: '#6366F1',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.2,
    shadowRadius: 12,
    elevation: 8,
  },
  shareGradient: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  shareIcon: {
    fontSize: 18,
  },
  summaryCard: {
    backgroundColor: '#FFFFFF',
    borderRadius: 20,
    paddingVertical: 20,
    paddingHorizontal: 24,
    marginHorizontal: 24,
    shadowColor: '#64748B',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.08,
    shadowRadius: 16,
    elevation: 8,
  },
  summaryContent: {
    flexDirection: 'row',
    justifyContent: 'space-around',
  },
  summaryItem: {
    alignItems: 'center',
  },
  summaryNumber: {
    fontSize: 28,
    fontWeight: '800',
    color: '#6366F1',
    marginBottom: 4,
  },
  summaryLabel: {
    fontSize: 12,
    fontWeight: '600',
    color: '#64748B',
    textTransform: 'uppercase',
    letterSpacing: 0.5,
  },
  timelineContainer: {
    flex: 1,
  },
  timelineContent: {
    paddingHorizontal: 24,
    paddingTop: 24,
    paddingBottom: 100,
  },
  timelineItem: {
    flexDirection: 'row',
    marginBottom: 24,
  },
  timelineLeft: {
    alignItems: 'center',
    marginRight: 20,
    width: 70,
  },
  timeContainer: {
    backgroundColor: '#FFFFFF',
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderRadius: 12,
    marginBottom: 12,
    shadowColor: '#64748B',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 4,
  },
  timeText: {
    fontSize: 12,
    fontWeight: '700',
    color: '#6366F1',
    textAlign: 'center',
  },
  timelineDot: {
    width: 16,
    height: 16,
    borderRadius: 8,
    backgroundColor: '#6366F1',
    marginBottom: 8,
    shadowColor: '#6366F1',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.3,
    shadowRadius: 4,
    elevation: 4,
  },
  timelineLine: {
    width: 3,
    flex: 1,
    backgroundColor: '#E2E8F0',
    borderRadius: 1.5,
  },
  activityCard: {
    flex: 1,
    backgroundColor: '#FFFFFF',
    borderRadius: 20,
    padding: 20,
    shadowColor: '#64748B',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.08,
    shadowRadius: 16,
    elevation: 8,
  },
  activityHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: 12,
  },
  activityTitle: {
    fontSize: 18,
    fontWeight: '700',
    color: '#1E293B',
    flex: 1,
    marginRight: 12,
  },
  activityMeta: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  metaIcon: {
    backgroundColor: '#F1F5F9',
    width: 32,
    height: 32,
    borderRadius: 16,
    justifyContent: 'center',
    alignItems: 'center',
  },
  metaEmoji: {
    fontSize: 16,
  },
  moodContainer: {
    backgroundColor: '#EEF4FF',
    width: 36,
    height: 36,
    borderRadius: 18,
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 2,
    borderColor: '#6366F1',
  },
  moodEmoji: {
    fontSize: 18,
  },
  activityDescription: {
    fontSize: 15,
    lineHeight: 22,
    color: '#64748B',
    marginBottom: 16,
  },
  activityActions: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  editButton: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 12,
    backgroundColor: '#F1F5F9',
  },
  editButtonText: {
    fontSize: 14,
    fontWeight: '600',
    color: '#475569',
  },
  viewMediaButton: {
    borderRadius: 12,
    overflow: 'hidden',
    shadowColor: '#6366F1',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 8,
    elevation: 4,
  },
  viewMediaGradient: {
    paddingHorizontal: 16,
    paddingVertical: 8,
  },
  viewMediaText: {
    fontSize: 14,
    fontWeight: '600',
    color: '#FFFFFF',
  },
  addActivityContainer: {
    marginTop: 32,
    alignItems: 'center',
  },
  addActivityButton: {
    borderRadius: 20,
    overflow: 'hidden',
    shadowColor: '#6366F1',
    shadowOffset: { width: 0, height: 8 },
    shadowOpacity: 0.3,
    shadowRadius: 16,
    elevation: 12,
  },
  addActivityGradient: {
    paddingHorizontal: 32,
    paddingVertical: 16,
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  addActivityIcon: {
    fontSize: 20,
    fontWeight: '700',
    color: '#FFFFFF',
  },
  addActivityText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: '700',
  },
});

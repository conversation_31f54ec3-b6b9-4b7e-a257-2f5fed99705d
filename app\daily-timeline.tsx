import React, { useState } from 'react';
import { ScrollView, StyleSheet, View, Pressable, Dimensions, Alert } from 'react-native';
import { router, useLocalSearchParams } from 'expo-router';
import { StatusBar } from 'expo-status-bar';
import { LinearGradient } from 'expo-linear-gradient';

import { ThemedText } from '@/components/ThemedText';
import { GradientBackground } from '@/components/GradientBackground';
import { Card } from '@/components/Card';
import { useColorScheme } from '@/hooks/useColorScheme';

const { width } = Dimensions.get('window');

// Sample timeline data
const SAMPLE_TIMELINE = [
  {
    id: '1',
    time: '05:00',
    activity: 'Woke up',
    description: 'Started the day early, feeling refreshed',
    hasVoice: false,
    hasPhoto: false,
    mood: 'good',
  },
  {
    id: '2',
    time: '08:00',
    activity: 'Breakfast',
    description: 'Had a healthy breakfast with fruits and coffee',
    hasVoice: true,
    hasPhoto: true,
    mood: 'excellent',
  },
  {
    id: '3',
    time: '09:30',
    activity: 'Work Session',
    description: 'Deep focus work on the new project',
    hasVoice: false,
    hasPhoto: false,
    mood: 'good',
  },
  {
    id: '4',
    time: '12:00',
    activity: 'Lunch Break',
    description: 'Had lunch with colleagues, great conversations',
    hasVoice: true,
    hasPhoto: true,
    mood: 'excellent',
  },
  {
    id: '5',
    time: '18:00',
    activity: 'Gym Workout',
    description: 'Intense cardio and strength training session',
    hasVoice: false,
    hasPhoto: true,
    mood: 'excellent',
  },
];

export default function DailyTimelineScreen() {
  const { date } = useLocalSearchParams();
  const [timeline, setTimeline] = useState(SAMPLE_TIMELINE);
  const colorScheme = useColorScheme();

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', { 
      weekday: 'long', 
      year: 'numeric', 
      month: 'long', 
      day: 'numeric' 
    });
  };

  const getMoodEmoji = (mood: string) => {
    switch (mood) {
      case 'excellent': return '😄';
      case 'good': return '😊';
      case 'neutral': return '😐';
      case 'bad': return '😔';
      case 'terrible': return '😢';
      default: return '😐';
    }
  };

  const handleShare = () => {
    Alert.alert(
      'Share Day',
      'Share your entire day with friends and family?',
      [
        { text: 'Cancel', style: 'cancel' },
        { text: 'Share', onPress: () => console.log('Sharing day...') }
      ]
    );
  };

  const renderTimelineItem = (item: any, index: number) => {
    return (
      <View key={item.id} style={styles.timelineItem}>
        {/* Time indicator */}
        <View style={styles.timelineLeft}>
          <View style={styles.timeContainer}>
            <ThemedText style={styles.timeText}>{item.time}</ThemedText>
          </View>
          <View style={styles.timelineDot} />
          {index < timeline.length - 1 && <View style={styles.timelineLine} />}
        </View>

        {/* Activity card */}
        <Card variant="ios" style={styles.activityCard}>
          <LinearGradient
            colors={['rgba(102, 126, 234, 0.05)', 'rgba(240, 147, 251, 0.05)']}
            style={styles.activityGradient}
          >
            <View style={styles.activityHeader}>
              <ThemedText style={styles.activityTitle}>{item.activity}</ThemedText>
              <View style={styles.activityMeta}>
                {item.hasVoice && (
                  <View style={styles.metaIcon}>
                    <ThemedText style={styles.metaEmoji}>🎤</ThemedText>
                  </View>
                )}
                {item.hasPhoto && (
                  <View style={styles.metaIcon}>
                    <ThemedText style={styles.metaEmoji}>📷</ThemedText>
                  </View>
                )}
                <View style={styles.moodContainer}>
                  <ThemedText style={styles.moodEmoji}>
                    {getMoodEmoji(item.mood)}
                  </ThemedText>
                </View>
              </View>
            </View>
            
            <ThemedText style={styles.activityDescription}>
              {item.description}
            </ThemedText>

            <View style={styles.activityActions}>
              <Pressable style={styles.editButton}>
                <ThemedText style={styles.editButtonText}>Edit</ThemedText>
              </Pressable>
              {(item.hasVoice || item.hasPhoto) && (
                <Pressable style={styles.viewMediaButton}>
                  <LinearGradient colors={['#667eea', '#764ba2']} style={styles.viewMediaGradient}>
                    <ThemedText style={styles.viewMediaText}>View Media</ThemedText>
                  </LinearGradient>
                </Pressable>
              )}
            </View>
          </LinearGradient>
        </Card>
      </View>
    );
  };

  return (
    <GradientBackground variant="primary">
      <StatusBar style={colorScheme === 'dark' ? 'light' : 'dark'} />
      
      {/* Header */}
      <View style={styles.header}>
        <View style={styles.headerTop}>
          <Pressable onPress={() => router.back()} style={styles.backButton}>
            <LinearGradient colors={['rgba(255,255,255,0.2)', 'rgba(255,255,255,0.1)']} style={styles.backGradient}>
              <ThemedText style={styles.backText}>←</ThemedText>
            </LinearGradient>
          </Pressable>
          
          <View style={styles.headerCenter}>
            <ThemedText style={styles.headerTitle}>Daily Timeline</ThemedText>
            <ThemedText style={styles.headerDate}>
              {date ? formatDate(date as string) : 'Today'}
            </ThemedText>
          </View>
          
          <Pressable onPress={handleShare} style={styles.shareButton}>
            <LinearGradient colors={['#f093fb', '#f5576c']} style={styles.shareGradient}>
              <ThemedText style={styles.shareText}>📤</ThemedText>
            </LinearGradient>
          </Pressable>
        </View>

        {/* Day Summary */}
        <Card variant="ios" style={styles.summaryCard}>
          <View style={styles.summaryContent}>
            <View style={styles.summaryItem}>
              <ThemedText style={styles.summaryNumber}>{timeline.length}</ThemedText>
              <ThemedText style={styles.summaryLabel}>Activities</ThemedText>
            </View>
            <View style={styles.summaryItem}>
              <ThemedText style={styles.summaryNumber}>
                {timeline.filter(item => item.hasVoice).length}
              </ThemedText>
              <ThemedText style={styles.summaryLabel}>Voice Notes</ThemedText>
            </View>
            <View style={styles.summaryItem}>
              <ThemedText style={styles.summaryNumber}>
                {timeline.filter(item => item.hasPhoto).length}
              </ThemedText>
              <ThemedText style={styles.summaryLabel}>Photos</ThemedText>
            </View>
          </View>
        </Card>
      </View>

      {/* Timeline */}
      <ScrollView 
        style={styles.timelineContainer}
        showsVerticalScrollIndicator={false}
        contentContainerStyle={styles.timelineContent}
      >
        {timeline.map((item, index) => renderTimelineItem(item, index))}
        
        {/* Add Activity Button */}
        <View style={styles.addActivityContainer}>
          <Pressable 
            style={styles.addActivityButton}
            onPress={() => router.push('/add-activity')}
          >
            <LinearGradient
              colors={['#667eea', '#764ba2']}
              style={styles.addActivityGradient}
            >
              <ThemedText style={styles.addActivityText}>+ Add Activity</ThemedText>
            </LinearGradient>
          </Pressable>
        </View>
      </ScrollView>
    </GradientBackground>
  );
}

const styles = StyleSheet.create({
  header: {
    paddingTop: 60,
    paddingHorizontal: 20,
    paddingBottom: 20,
  },
  headerTop: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 20,
  },
  backButton: {
    width: 44,
    height: 44,
    borderRadius: 22,
    overflow: 'hidden',
  },
  backGradient: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  backText: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#FFFFFF',
  },
  headerCenter: {
    flex: 1,
    alignItems: 'center',
  },
  headerTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#FFFFFF',
    marginBottom: 4,
  },
  headerDate: {
    fontSize: 16,
    color: 'rgba(255, 255, 255, 0.8)',
  },
  shareButton: {
    width: 44,
    height: 44,
    borderRadius: 22,
    overflow: 'hidden',
  },
  shareGradient: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  shareText: {
    fontSize: 20,
  },
  summaryCard: {
    paddingVertical: 16,
    paddingHorizontal: 20,
  },
  summaryContent: {
    flexDirection: 'row',
    justifyContent: 'space-around',
  },
  summaryItem: {
    alignItems: 'center',
  },
  summaryNumber: {
    fontSize: 24,
    fontWeight: 'bold',
    marginBottom: 4,
  },
  summaryLabel: {
    fontSize: 12,
    opacity: 0.7,
  },
  timelineContainer: {
    flex: 1,
  },
  timelineContent: {
    paddingHorizontal: 20,
    paddingBottom: 100,
  },
  timelineItem: {
    flexDirection: 'row',
    marginBottom: 20,
  },
  timelineLeft: {
    alignItems: 'center',
    marginRight: 16,
    width: 60,
  },
  timeContainer: {
    backgroundColor: 'rgba(255, 255, 255, 0.9)',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 8,
    marginBottom: 8,
  },
  timeText: {
    fontSize: 12,
    fontWeight: '600',
    color: '#667eea',
  },
  timelineDot: {
    width: 12,
    height: 12,
    borderRadius: 6,
    backgroundColor: '#f093fb',
    marginBottom: 8,
    shadowColor: '#f093fb',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.3,
    shadowRadius: 4,
    elevation: 4,
  },
  timelineLine: {
    width: 2,
    flex: 1,
    backgroundColor: 'rgba(255, 255, 255, 0.3)',
  },
  activityCard: {
    flex: 1,
    padding: 0,
    overflow: 'hidden',
  },
  activityGradient: {
    padding: 16,
  },
  activityHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  activityTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    flex: 1,
  },
  activityMeta: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  metaIcon: {
    backgroundColor: 'rgba(102, 126, 234, 0.1)',
    width: 28,
    height: 28,
    borderRadius: 14,
    justifyContent: 'center',
    alignItems: 'center',
  },
  metaEmoji: {
    fontSize: 14,
  },
  moodContainer: {
    backgroundColor: 'rgba(255, 255, 255, 0.8)',
    width: 32,
    height: 32,
    borderRadius: 16,
    justifyContent: 'center',
    alignItems: 'center',
  },
  moodEmoji: {
    fontSize: 16,
  },
  activityDescription: {
    fontSize: 15,
    lineHeight: 20,
    opacity: 0.8,
    marginBottom: 16,
  },
  activityActions: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  editButton: {
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 8,
    backgroundColor: 'rgba(102, 126, 234, 0.1)',
  },
  editButtonText: {
    fontSize: 12,
    fontWeight: '600',
    color: '#667eea',
  },
  viewMediaButton: {
    borderRadius: 8,
    overflow: 'hidden',
  },
  viewMediaGradient: {
    paddingHorizontal: 12,
    paddingVertical: 6,
  },
  viewMediaText: {
    fontSize: 12,
    fontWeight: '600',
    color: '#FFFFFF',
  },
  addActivityContainer: {
    marginTop: 20,
    alignItems: 'center',
  },
  addActivityButton: {
    borderRadius: 16,
    overflow: 'hidden',
    shadowColor: '#667eea',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.3,
    shadowRadius: 8,
    elevation: 8,
  },
  addActivityGradient: {
    paddingHorizontal: 32,
    paddingVertical: 16,
    alignItems: 'center',
  },
  addActivityText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: 'bold',
  },
});

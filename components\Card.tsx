import { Colors } from '@/constants/Colors';
import { useColorScheme } from '@/hooks/useColorScheme';
import { useThemeColor } from '@/hooks/useThemeColor';
import React from 'react';
import { Pressable, StyleSheet, View, ViewStyle } from 'react-native';

interface CardProps {
  children: React.ReactNode;
  style?: ViewStyle;
  variant?: 'default' | 'elevated' | 'outlined' | 'glass' | 'ios' | 'gradient';
  onPress?: () => void;
  disabled?: boolean;
  gradient?: string[];
}

export function Card({
  children,
  style,
  variant = 'ios',
  onPress,
  disabled = false,
  gradient
}: CardProps) {
  const colorScheme = useColorScheme();
  const backgroundColor = useThemeColor({}, 'surface');
  const borderColor = useThemeColor({}, 'border');

  const getCardStyle = () => {
    const baseStyle = {
      borderRadius: 16,
      padding: 16,
    };

    switch (variant) {
      case 'ios':
        return {
          ...baseStyle,
          backgroundColor: colorScheme === 'dark'
            ? 'rgba(44, 44, 46, 0.95)'
            : 'rgba(255, 255, 255, 0.95)',
          borderRadius: 20,
          shadowColor: colorScheme === 'dark' ? '#000' : '#000',
          shadowOffset: {
            width: 0,
            height: 8,
          },
          shadowOpacity: colorScheme === 'dark' ? 0.3 : 0.1,
          shadowRadius: 20,
          elevation: 10,
          borderWidth: 0.5,
          borderColor: colorScheme === 'dark'
            ? 'rgba(255, 255, 255, 0.1)'
            : 'rgba(0, 0, 0, 0.05)',
        };

      case 'elevated':
        return {
          ...baseStyle,
          backgroundColor,
          shadowColor: Colors[colorScheme ?? 'light'].shadow,
          shadowOffset: {
            width: 0,
            height: 4,
          },
          shadowOpacity: 0.1,
          shadowRadius: 12,
          elevation: 8,
        };

      case 'outlined':
        return {
          ...baseStyle,
          borderWidth: 1,
          borderColor,
          backgroundColor: 'transparent',
        };

      case 'glass':
        return {
          ...baseStyle,
          backgroundColor: colorScheme === 'dark'
            ? 'rgba(255, 255, 255, 0.05)'
            : 'rgba(255, 255, 255, 0.8)',
          backdropFilter: 'blur(10px)',
          borderWidth: 1,
          borderColor: colorScheme === 'dark'
            ? 'rgba(255, 255, 255, 0.1)'
            : 'rgba(255, 255, 255, 0.2)',
        };

      case 'gradient':
        return {
          ...baseStyle,
          backgroundColor: 'transparent',
          borderRadius: 20,
          shadowColor: '#000',
          shadowOffset: {
            width: 0,
            height: 8,
          },
          shadowOpacity: 0.15,
          shadowRadius: 20,
          elevation: 10,
        };

      default:
        return {
          ...baseStyle,
          backgroundColor,
          shadowColor: Colors[colorScheme ?? 'light'].shadowLight,
          shadowOffset: {
            width: 0,
            height: 2,
          },
          shadowOpacity: 0.05,
          shadowRadius: 8,
          elevation: 2,
        };
    }
  };

  const cardStyle = getCardStyle();

  const renderContent = () => {
    if (variant === 'gradient' && gradient) {
      return (
        <LinearGradient
          colors={gradient}
          style={[cardStyle, style]}
          start={{ x: 0, y: 0 }}
          end={{ x: 1, y: 1 }}
        >
          {children}
        </LinearGradient>
      );
    }

    if (variant === 'glass') {
      return (
        <BlurView
          intensity={80}
          style={[cardStyle, style]}
          tint={colorScheme === 'dark' ? 'dark' : 'light'}
        >
          {children}
        </BlurView>
      );
    }

    return (
      <View style={[cardStyle, style]}>
        {children}
      </View>
    );
  };

  if (onPress) {
    return (
      <Pressable
        onPress={onPress}
        disabled={disabled}
        style={({ pressed }) => [
          pressed && styles.pressed,
          disabled && styles.disabled,
        ]}
      >
        {renderContent()}
      </Pressable>
    );
  }

  return renderContent();
}

const styles = StyleSheet.create({
  pressed: {
    opacity: 0.8,
    transform: [{ scale: 0.98 }],
  },
  disabled: {
    opacity: 0.5,
  },
});

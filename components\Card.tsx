import React from 'react';
import { View, ViewStyle, StyleSheet, Pressable } from 'react-native';
import { useThemeColor } from '@/hooks/useThemeColor';
import { Colors } from '@/constants/Colors';
import { useColorScheme } from '@/hooks/useColorScheme';

interface CardProps {
  children: React.ReactNode;
  style?: ViewStyle;
  variant?: 'default' | 'elevated' | 'outlined' | 'glass';
  onPress?: () => void;
  disabled?: boolean;
}

export function Card({ 
  children, 
  style, 
  variant = 'default',
  onPress,
  disabled = false
}: CardProps) {
  const colorScheme = useColorScheme();
  const backgroundColor = useThemeColor({}, 'surface');
  const borderColor = useThemeColor({}, 'border');
  
  const getCardStyle = () => {
    const baseStyle = {
      backgroundColor,
      borderRadius: 16,
      padding: 16,
    };

    switch (variant) {
      case 'elevated':
        return {
          ...baseStyle,
          shadowColor: Colors[colorScheme ?? 'light'].shadow,
          shadowOffset: {
            width: 0,
            height: 4,
          },
          shadowOpacity: 0.1,
          shadowRadius: 12,
          elevation: 8,
        };
      
      case 'outlined':
        return {
          ...baseStyle,
          borderWidth: 1,
          borderColor,
          backgroundColor: 'transparent',
        };
      
      case 'glass':
        return {
          ...baseStyle,
          backgroundColor: colorScheme === 'dark' 
            ? 'rgba(255, 255, 255, 0.05)' 
            : 'rgba(255, 255, 255, 0.8)',
          backdropFilter: 'blur(10px)',
          borderWidth: 1,
          borderColor: colorScheme === 'dark'
            ? 'rgba(255, 255, 255, 0.1)'
            : 'rgba(255, 255, 255, 0.2)',
        };
      
      default:
        return {
          ...baseStyle,
          shadowColor: Colors[colorScheme ?? 'light'].shadowLight,
          shadowOffset: {
            width: 0,
            height: 2,
          },
          shadowOpacity: 0.05,
          shadowRadius: 8,
          elevation: 2,
        };
    }
  };

  const cardStyle = getCardStyle();

  if (onPress) {
    return (
      <Pressable
        onPress={onPress}
        disabled={disabled}
        style={({ pressed }) => [
          cardStyle,
          style,
          pressed && styles.pressed,
          disabled && styles.disabled,
        ]}
      >
        {children}
      </Pressable>
    );
  }

  return (
    <View style={[cardStyle, style]}>
      {children}
    </View>
  );
}

const styles = StyleSheet.create({
  pressed: {
    opacity: 0.8,
    transform: [{ scale: 0.98 }],
  },
  disabled: {
    opacity: 0.5,
  },
});

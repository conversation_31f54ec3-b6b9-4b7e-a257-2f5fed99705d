import { useColorScheme } from '@/hooks/useColorScheme';
import { LinearGradient } from 'expo-linear-gradient';
import React from 'react';
import { ViewStyle } from 'react-native';

interface GradientBackgroundProps {
  children: React.ReactNode;
  style?: ViewStyle;
  variant?: 'primary' | 'secondary' | 'accent' | 'mood' | 'ios' | 'card';
  moodType?: 'happy' | 'sad' | 'excited' | 'anxious' | 'calm';
  intensity?: number;
  withBlur?: boolean;
}

export function GradientBackground({
  children,
  style,
  variant = 'ios',
  moodType = 'happy',
  intensity = 100,
  withBlur = false
}: GradientBackgroundProps) {
  const colorScheme = useColorScheme();
  const isDark = colorScheme === 'dark';

  const getGradientColors = () => {
    switch (variant) {
      case 'ios':
        return isDark
          ? ['#1C1C1E', '#2C2C2E', '#1C1C1E']
          : ['#F2F2F7', '#FFFFFF', '#F8F8F8'];

      case 'primary':
        return isDark
          ? ['#1E1B4B', '#312E81', '#1E1B4B']
          : ['#667eea', '#764ba2', '#f093fb'];

      case 'secondary':
        return isDark
          ? ['#374151', '#1F2937', '#111827']
          : ['#F9FAFB', '#F3F4F6', '#E5E7EB'];

      case 'accent':
        return isDark
          ? ['#7C2D12', '#451A03', '#1C1917']
          : ['#FEF3C7', '#FDE68A', '#F59E0B'];

      case 'card':
        return isDark
          ? ['rgba(44, 44, 46, 0.95)', 'rgba(28, 28, 30, 0.95)']
          : ['rgba(255, 255, 255, 0.95)', 'rgba(248, 248, 248, 0.95)'];

      case 'mood':
        switch (moodType) {
          case 'happy':
            return isDark
              ? ['#451A03', '#1C1917', '#0C0A09']
              : ['#FFFBEB', '#FEF3C7', '#FDE68A'];
          case 'sad':
            return isDark
              ? ['#1E3A8A', '#1E40AF', '#1D4ED8']
              : ['#EFF6FF', '#DBEAFE', '#BFDBFE'];
          case 'excited':
            return isDark
              ? ['#831843', '#BE185D', '#E11D48']
              : ['#FDF2F8', '#FCE7F3', '#FBCFE8'];
          case 'anxious':
            return isDark
              ? ['#991B1B', '#DC2626', '#EF4444']
              : ['#FEF2F2', '#FEE2E2', '#FECACA'];
          case 'calm':
            return isDark
              ? ['#064E3B', '#065F46', '#047857']
              : ['#ECFDF5', '#D1FAE5', '#A7F3D0'];
          default:
            return isDark
              ? ['#1C1C1E', '#2C2C2E', '#1C1C1E']
              : ['#F2F2F7', '#FFFFFF', '#F8F8F8'];
        }

      default:
        return isDark
          ? ['#1C1C1E', '#2C2C2E', '#1C1C1E']
          : ['#F2F2F7', '#FFFFFF', '#F8F8F8'];
    }
  };

  const gradientColors = getGradientColors();

  if (withBlur) {
    return (
      <LinearGradient
        colors={gradientColors}
        style={[{ flex: 1 }, style]}
        start={{ x: 0, y: 0 }}
        end={{ x: 1, y: 1 }}
      >
        <BlurView
          intensity={intensity}
          style={{ flex: 1 }}
          tint={isDark ? 'dark' : 'light'}
        >
          {children}
        </BlurView>
      </LinearGradient>
    );
  }

  return (
    <LinearGradient
      colors={gradientColors}
      style={[{ flex: 1 }, style]}
      start={{ x: 0, y: 0 }}
      end={{ x: 1, y: 1 }}
      locations={[0, 0.5, 1]}
    >
      {children}
    </LinearGradient>
  );
}

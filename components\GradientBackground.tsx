import React from 'react';
import { LinearGradient } from 'expo-linear-gradient';
import { ViewStyle } from 'react-native';
import { useColorScheme } from '@/hooks/useColorScheme';

interface GradientBackgroundProps {
  children: React.ReactNode;
  style?: ViewStyle;
  variant?: 'primary' | 'secondary' | 'accent' | 'mood';
  moodType?: 'happy' | 'sad' | 'excited' | 'anxious' | 'calm';
}

export function GradientBackground({ 
  children, 
  style, 
  variant = 'primary',
  moodType = 'happy'
}: GradientBackgroundProps) {
  const colorScheme = useColorScheme();
  const isDark = colorScheme === 'dark';

  const getGradientColors = () => {
    switch (variant) {
      case 'primary':
        return isDark 
          ? ['#1F2937', '#111827', '#0F172A']
          : ['#FFFFFF', '#F8FAFC', '#F1F5F9'];
      
      case 'secondary':
        return isDark
          ? ['#374151', '#1F2937', '#111827']
          : ['#F9FAFB', '#F3F4F6', '#E5E7EB'];
      
      case 'accent':
        return isDark
          ? ['#7C2D12', '#451A03', '#1C1917']
          : ['#FEF3C7', '#FDE68A', '#F59E0B'];
      
      case 'mood':
        switch (moodType) {
          case 'happy':
            return isDark
              ? ['#451A03', '#1C1917', '#0C0A09']
              : ['#FEF3C7', '#FDE68A', '#FBBF24'];
          case 'sad':
            return isDark
              ? ['#1E3A8A', '#1E40AF', '#1D4ED8']
              : ['#DBEAFE', '#BFDBFE', '#93C5FD'];
          case 'excited':
            return isDark
              ? ['#831843', '#BE185D', '#E11D48']
              : ['#FCE7F3', '#FBCFE8', '#F9A8D4'];
          case 'anxious':
            return isDark
              ? ['#991B1B', '#DC2626', '#EF4444']
              : ['#FEE2E2', '#FECACA', '#FCA5A5'];
          case 'calm':
            return isDark
              ? ['#064E3B', '#065F46', '#047857']
              : ['#D1FAE5', '#A7F3D0', '#6EE7B7'];
          default:
            return isDark
              ? ['#1F2937', '#111827', '#0F172A']
              : ['#FFFFFF', '#F8FAFC', '#F1F5F9'];
        }
      
      default:
        return isDark
          ? ['#1F2937', '#111827', '#0F172A']
          : ['#FFFFFF', '#F8FAFC', '#F1F5F9'];
    }
  };

  return (
    <LinearGradient
      colors={getGradientColors()}
      style={[{ flex: 1 }, style]}
      start={{ x: 0, y: 0 }}
      end={{ x: 1, y: 1 }}
    >
      {children}
    </LinearGradient>
  );
}

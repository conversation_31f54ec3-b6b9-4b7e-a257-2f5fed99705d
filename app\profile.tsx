import React from 'react';
import { View, StyleSheet, Pressable } from 'react-native';
import { router } from 'expo-router';
import { StatusBar } from 'expo-status-bar';
import { LinearGradient } from 'expo-linear-gradient';

import { ThemedText } from '@/components/ThemedText';
import { GradientBackground } from '@/components/GradientBackground';
import { useColorScheme } from '@/hooks/useColorScheme';

export default function ProfileScreen() {
  const colorScheme = useColorScheme();

  return (
    <GradientBackground variant="primary">
      <StatusBar style="dark" />
      
      {/* Header */}
      <View style={styles.header}>
        <View style={styles.headerTop}>
          <Pressable onPress={() => router.back()} style={styles.backButton}>
            <View style={styles.backContainer}>
              <ThemedText style={styles.backIcon}>←</ThemedText>
            </View>
          </Pressable>
          
          <View style={styles.headerCenter}>
            <ThemedText style={styles.headerTitle}>Profile</ThemedText>
            <ThemedText style={styles.headerSubtitle}>Manage your account</ThemedText>
          </View>
          
          <View style={styles.placeholder} />
        </View>
      </View>

      {/* Profile Content */}
      <View style={styles.content}>
        {/* Profile Card */}
        <View style={styles.profileCard}>
          <View style={styles.avatarContainer}>
            <View style={styles.avatar}>
              <ThemedText style={styles.avatarText}>S</ThemedText>
            </View>
          </View>
          
          <ThemedText style={styles.userName}>Sanid</ThemedText>
          <ThemedText style={styles.userEmail}><EMAIL></ThemedText>
          
          <Pressable style={styles.editButton}>
            <LinearGradient 
              colors={['#6366F1', '#8B5CF6']} 
              style={styles.editGradient}
            >
              <ThemedText style={styles.editText}>Edit Profile</ThemedText>
            </LinearGradient>
          </Pressable>
        </View>

        {/* Menu Items */}
        <View style={styles.menuSection}>
          <Pressable style={styles.menuItem}>
            <ThemedText style={styles.menuIcon}>📊</ThemedText>
            <ThemedText style={styles.menuText}>Statistics</ThemedText>
            <ThemedText style={styles.menuArrow}>›</ThemedText>
          </Pressable>
          
          <Pressable style={styles.menuItem}>
            <ThemedText style={styles.menuIcon}>🔔</ThemedText>
            <ThemedText style={styles.menuText}>Notifications</ThemedText>
            <ThemedText style={styles.menuArrow}>›</ThemedText>
          </Pressable>
          
          <Pressable style={styles.menuItem}>
            <ThemedText style={styles.menuIcon}>🎨</ThemedText>
            <ThemedText style={styles.menuText}>Appearance</ThemedText>
            <ThemedText style={styles.menuArrow}>›</ThemedText>
          </Pressable>
          
          <Pressable style={styles.menuItem}>
            <ThemedText style={styles.menuIcon}>📤</ThemedText>
            <ThemedText style={styles.menuText}>Export Data</ThemedText>
            <ThemedText style={styles.menuArrow}>›</ThemedText>
          </Pressable>
          
          <Pressable style={styles.menuItem}>
            <ThemedText style={styles.menuIcon}>❓</ThemedText>
            <ThemedText style={styles.menuText}>Help & Support</ThemedText>
            <ThemedText style={styles.menuArrow}>›</ThemedText>
          </Pressable>
        </View>
      </View>
    </GradientBackground>
  );
}

const styles = StyleSheet.create({
  header: {
    paddingTop: 60,
    paddingHorizontal: 24,
    paddingBottom: 32,
  },
  headerTop: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  backButton: {
    width: 48,
    height: 48,
  },
  backContainer: {
    width: 48,
    height: 48,
    borderRadius: 24,
    backgroundColor: '#FFFFFF',
    justifyContent: 'center',
    alignItems: 'center',
    shadowColor: '#64748B',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 4,
  },
  backIcon: {
    fontSize: 20,
    fontWeight: '600',
    color: '#475569',
  },
  headerCenter: {
    flex: 1,
    alignItems: 'center',
  },
  headerTitle: {
    fontSize: 24,
    fontWeight: '700',
    color: '#1E293B',
    letterSpacing: -0.5,
  },
  headerSubtitle: {
    fontSize: 14,
    fontWeight: '500',
    color: '#64748B',
    marginTop: 2,
  },
  placeholder: {
    width: 48,
  },
  content: {
    flex: 1,
    paddingHorizontal: 24,
  },
  profileCard: {
    backgroundColor: '#FFFFFF',
    borderRadius: 24,
    paddingVertical: 32,
    paddingHorizontal: 24,
    alignItems: 'center',
    marginBottom: 24,
    shadowColor: '#64748B',
    shadowOffset: { width: 0, height: 8 },
    shadowOpacity: 0.1,
    shadowRadius: 24,
    elevation: 12,
  },
  avatarContainer: {
    marginBottom: 16,
  },
  avatar: {
    width: 80,
    height: 80,
    borderRadius: 40,
    backgroundColor: '#6366F1',
    justifyContent: 'center',
    alignItems: 'center',
    shadowColor: '#6366F1',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.3,
    shadowRadius: 12,
    elevation: 8,
  },
  avatarText: {
    fontSize: 32,
    fontWeight: '700',
    color: '#FFFFFF',
  },
  userName: {
    fontSize: 24,
    fontWeight: '700',
    color: '#1E293B',
    marginBottom: 4,
  },
  userEmail: {
    fontSize: 16,
    fontWeight: '500',
    color: '#64748B',
    marginBottom: 24,
  },
  editButton: {
    borderRadius: 16,
    overflow: 'hidden',
    shadowColor: '#6366F1',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.2,
    shadowRadius: 12,
    elevation: 8,
  },
  editGradient: {
    paddingHorizontal: 24,
    paddingVertical: 12,
  },
  editText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: '700',
  },
  menuSection: {
    backgroundColor: '#FFFFFF',
    borderRadius: 20,
    paddingVertical: 8,
    shadowColor: '#64748B',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.08,
    shadowRadius: 16,
    elevation: 8,
  },
  menuItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 16,
    paddingHorizontal: 20,
  },
  menuIcon: {
    fontSize: 20,
    marginRight: 16,
  },
  menuText: {
    flex: 1,
    fontSize: 16,
    fontWeight: '600',
    color: '#1E293B',
  },
  menuArrow: {
    fontSize: 20,
    fontWeight: '600',
    color: '#94A3B8',
  },
});

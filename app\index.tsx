import { LinearGradient } from 'expo-linear-gradient';
import { router } from 'expo-router';
import { StatusBar } from 'expo-status-bar';
import { useState } from 'react';
import { Dimensions, Pressable, StyleSheet, View } from 'react-native';

import { GradientBackground } from '@/components/GradientBackground';
import { ThemedText } from '@/components/ThemedText';
import { useJournal } from '@/context/JournalContext';
import { useColorScheme } from '@/hooks/useColorScheme';

const { width } = Dimensions.get('window');



export default function CalendarHomeScreen() {
  const [selectedDate, setSelectedDate] = useState(new Date());
  const [currentMonth, setCurrentMonth] = useState(new Date());
  const colorScheme = useColorScheme();
  const { getActivitiesForDate } = useJournal();

  // Get calendar data
  const getDaysInMonth = (date: Date) => {
    const year = date.getFullYear();
    const month = date.getMonth();
    const firstDay = new Date(year, month, 1);
    const lastDay = new Date(year, month + 1, 0);
    const daysInMonth = lastDay.getDate();
    const startingDayOfWeek = firstDay.getDay();

    const days = [];

    // Add empty cells for days before the first day of the month
    for (let i = 0; i < startingDayOfWeek; i++) {
      days.push(null);
    }

    // Add all days of the month
    for (let day = 1; day <= daysInMonth; day++) {
      days.push(new Date(year, month, day));
    }

    return days;
  };

  const formatDateKey = (date: Date) => {
    return date.toISOString().split('T')[0];
  };

  const isToday = (date: Date) => {
    const today = new Date();
    return date.toDateString() === today.toDateString();
  };

  const isSameDay = (date1: Date, date2: Date) => {
    return date1.toDateString() === date2.toDateString();
  };

  const hasEntries = (date: Date) => {
    const dateKey = formatDateKey(date);
    return getActivitiesForDate(dateKey);
  };

  const handleDateSelect = (date: Date) => {
    setSelectedDate(date);
    const dateKey = formatDateKey(date);
    const activities = getActivitiesForDate(dateKey);
    if (activities && activities.length > 0) {
      router.push(`/daily-timeline?date=${dateKey}`);
    } else {
      router.push('/add-activity');
    }
  };

  const navigateMonth = (direction: 'prev' | 'next') => {
    const newMonth = new Date(currentMonth);
    if (direction === 'prev') {
      newMonth.setMonth(newMonth.getMonth() - 1);
    } else {
      newMonth.setMonth(newMonth.getMonth() + 1);
    }
    setCurrentMonth(newMonth);
  };

  const renderCalendarDay = (date: Date | null, index: number) => {
    if (!date) {
      return <View key={index} style={styles.emptyDay} />;
    }

    const entries = hasEntries(date);
    const isSelected = isSameDay(date, selectedDate);
    const todayDate = isToday(date);

    return (
      <Pressable
        key={index}
        style={[
          styles.calendarDay,
          isSelected && styles.selectedDay,
          todayDate && styles.todayDay,
        ]}
        onPress={() => handleDateSelect(date)}
      >
        <View style={[
          styles.dayContainer,
          isSelected && styles.selectedDayContainer,
          todayDate && styles.todayDayContainer,
        ]}>
          <ThemedText
            style={[
              styles.dayText,
              isSelected && styles.selectedDayText,
              todayDate && styles.todayDayText,
            ]}
          >
            {date.getDate()}
          </ThemedText>

          {entries && (
            <View style={styles.entriesIndicator}>
              <View style={[
                styles.entryDot,
                isSelected && styles.selectedEntryDot,
                todayDate && styles.todayEntryDot,
              ]} />
            </View>
          )}
        </View>
      </Pressable>
    );
  };

  const monthNames = [
    'January', 'February', 'March', 'April', 'May', 'June',
    'July', 'August', 'September', 'October', 'November', 'December'
  ];

  const dayNames = ['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'];

  return (
    <GradientBackground variant="primary">
      <StatusBar style="dark" />

      {/* Modern Header */}
      <View style={styles.header}>
        <View style={styles.headerTop}>
          <Pressable onPress={() => router.push('/stats')} style={styles.profileButton}>
            <View style={styles.profileContainer}>
              <View style={styles.profileAvatar}>
                <ThemedText style={styles.profileInitial}>S</ThemedText>
              </View>
            </View>
          </Pressable>

          <View style={styles.headerCenter}>
            <ThemedText style={styles.headerGreeting}>Good morning</ThemedText>
            <ThemedText style={styles.headerTitle}>Daily Journal</ThemedText>
          </View>

          <Pressable onPress={() => router.push('/add-activity')} style={styles.addButton}>
            <View style={styles.addContainer}>
              <LinearGradient
                colors={['#6366F1', '#8B5CF6']}
                style={styles.addGradient}
                start={{ x: 0, y: 0 }}
                end={{ x: 1, y: 1 }}
              >
                <ThemedText style={styles.addIcon}>+</ThemedText>
              </LinearGradient>
            </View>
          </Pressable>
        </View>
      </View>

      {/* Modern Calendar */}
      <View style={styles.calendarContainer}>
        {/* Month Navigation */}
        <View style={styles.monthSection}>
          <View style={styles.monthHeader}>
            <Pressable onPress={() => navigateMonth('prev')} style={styles.navButton}>
              <ThemedText style={styles.navText}>‹</ThemedText>
            </Pressable>
            <View style={styles.monthTitleContainer}>
              <ThemedText style={styles.monthTitle}>
                {monthNames[currentMonth.getMonth()]}
              </ThemedText>
              <ThemedText style={styles.yearTitle}>
                {currentMonth.getFullYear()}
              </ThemedText>
            </View>
            <Pressable onPress={() => navigateMonth('next')} style={styles.navButton}>
              <ThemedText style={styles.navText}>›</ThemedText>
            </Pressable>
          </View>
        </View>

        {/* Calendar Card */}
        <View style={styles.calendarCard}>
          {/* Day Headers */}
          <View style={styles.dayHeaders}>
            {dayNames.map((day) => (
              <View key={day} style={styles.dayHeader}>
                <ThemedText style={styles.dayHeaderText}>{day}</ThemedText>
              </View>
            ))}
          </View>

          {/* Calendar Grid */}
          <View style={styles.calendarGrid}>
            {getDaysInMonth(currentMonth).map((date, index) => renderCalendarDay(date, index))}
          </View>
        </View>
      </View>

      {/* Modern Today Summary */}
      <View style={styles.todaySection}>
        <View style={styles.todayCard}>
          <View style={styles.todayHeader}>
            <View style={styles.todayTitleContainer}>
              <ThemedText style={styles.todayTitle}>Today's Activities</ThemedText>
              <ThemedText style={styles.todayDate}>
                {selectedDate.toLocaleDateString('en-US', {
                  weekday: 'long',
                  month: 'long',
                  day: 'numeric'
                })}
              </ThemedText>
            </View>
          </View>

          {hasEntries(selectedDate) ? (
            <View style={styles.todayContent}>
              <View style={styles.statsRow}>
                <View style={styles.statItem}>
                  <ThemedText style={styles.statNumber}>
                    {hasEntries(selectedDate)?.length}
                  </ThemedText>
                  <ThemedText style={styles.statLabel}>Activities</ThemedText>
                </View>
                <View style={styles.statItem}>
                  <ThemedText style={styles.statNumber}>
                    {hasEntries(selectedDate)?.filter(e => e.hasVoice).length}
                  </ThemedText>
                  <ThemedText style={styles.statLabel}>Voice Notes</ThemedText>
                </View>
                <View style={styles.statItem}>
                  <ThemedText style={styles.statNumber}>
                    {hasEntries(selectedDate)?.filter(e => e.hasPhoto).length}
                  </ThemedText>
                  <ThemedText style={styles.statLabel}>Photos</ThemedText>
                </View>
              </View>

              <Pressable
                onPress={() => handleDateSelect(selectedDate)}
                style={styles.viewDayButton}
              >
                <LinearGradient
                  colors={['#6366F1', '#8B5CF6']}
                  style={styles.viewDayGradient}
                  start={{ x: 0, y: 0 }}
                  end={{ x: 1, y: 1 }}
                >
                  <ThemedText style={styles.viewDayText}>View Timeline</ThemedText>
                </LinearGradient>
              </Pressable>
            </View>
          ) : (
            <View style={styles.emptyState}>
              <View style={styles.emptyIcon}>
                <ThemedText style={styles.emptyIconText}>📝</ThemedText>
              </View>
              <ThemedText style={styles.emptyTitle}>No activities yet</ThemedText>
              <ThemedText style={styles.emptySubtitle}>Start logging your day</ThemedText>
              <Pressable
                onPress={() => handleDateSelect(selectedDate)}
                style={styles.addFirstButton}
              >
                <LinearGradient
                  colors={['#6366F1', '#8B5CF6']}
                  style={styles.addFirstGradient}
                  start={{ x: 0, y: 0 }}
                  end={{ x: 1, y: 1 }}
                >
                  <ThemedText style={styles.addFirstText}>Add Activity</ThemedText>
                </LinearGradient>
              </Pressable>
            </View>
          )}
        </View>
      </View>

      {/* Bottom Navigation */}
      <View style={styles.bottomNav}>
        <Pressable style={[styles.navItem, styles.navItemActive]}>
          <ThemedText style={[styles.navIcon, styles.navIconActive]}>🏠</ThemedText>
          <ThemedText style={[styles.navLabel, styles.navLabelActive]}>Home</ThemedText>
        </Pressable>

        <Pressable style={styles.navItem} onPress={() => router.push('/timeline')}>
          <ThemedText style={styles.navIcon}>📅</ThemedText>
          <ThemedText style={styles.navLabel}>Timeline</ThemedText>
        </Pressable>

        <Pressable style={styles.navItem} onPress={() => router.push('/add-activity')}>
          <View style={styles.navAddButton}>
            <LinearGradient
              colors={['#6366F1', '#8B5CF6']}
              style={styles.navAddGradient}
            >
              <ThemedText style={styles.navAddIcon}>+</ThemedText>
            </LinearGradient>
          </View>
        </Pressable>

        <Pressable style={styles.navItem} onPress={() => router.push('/stats')}>
          <ThemedText style={styles.navIcon}>📊</ThemedText>
          <ThemedText style={styles.navLabel}>Stats</ThemedText>
        </Pressable>

        <Pressable style={styles.navItem} onPress={() => router.push('/profile')}>
          <ThemedText style={styles.navIcon}>👤</ThemedText>
          <ThemedText style={styles.navLabel}>Profile</ThemedText>
        </Pressable>
      </View>
    </GradientBackground>
  );
}

const styles = StyleSheet.create({
  // Modern Header Styles
  header: {
    paddingTop: 60,
    paddingHorizontal: 24,
    paddingBottom: 32,
  },
  headerTop: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  profileButton: {
    width: 48,
    height: 48,
  },
  profileContainer: {
    width: 48,
    height: 48,
    borderRadius: 24,
    backgroundColor: '#FFFFFF',
    shadowColor: '#6366F1',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.1,
    shadowRadius: 12,
    elevation: 8,
  },
  profileAvatar: {
    width: 48,
    height: 48,
    borderRadius: 24,
    backgroundColor: '#6366F1',
    justifyContent: 'center',
    alignItems: 'center',
  },
  profileInitial: {
    fontSize: 18,
    fontWeight: '700',
    color: '#FFFFFF',
  },
  headerCenter: {
    flex: 1,
    alignItems: 'center',
  },
  headerGreeting: {
    fontSize: 14,
    fontWeight: '500',
    color: '#64748B',
    marginBottom: 2,
  },
  headerTitle: {
    fontSize: 24,
    fontWeight: '700',
    color: '#1E293B',
    letterSpacing: -0.5,
  },
  addButton: {
    width: 48,
    height: 48,
  },
  addContainer: {
    width: 48,
    height: 48,
    borderRadius: 24,
    shadowColor: '#6366F1',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.2,
    shadowRadius: 12,
    elevation: 8,
  },
  addGradient: {
    width: 48,
    height: 48,
    borderRadius: 24,
    justifyContent: 'center',
    alignItems: 'center',
  },
  addIcon: {
    fontSize: 20,
    fontWeight: '600',
    color: '#FFFFFF',
  },
  // Modern Calendar Styles
  calendarContainer: {
    flex: 1,
    paddingHorizontal: 24,
    paddingBottom: 100, // Space for bottom nav
  },
  monthSection: {
    marginBottom: 24,
  },
  monthHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  navButton: {
    width: 44,
    height: 44,
    borderRadius: 22,
    backgroundColor: '#FFFFFF',
    justifyContent: 'center',
    alignItems: 'center',
    shadowColor: '#64748B',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 4,
  },
  navText: {
    fontSize: 20,
    fontWeight: '600',
    color: '#475569',
  },
  monthTitleContainer: {
    alignItems: 'center',
  },
  monthTitle: {
    fontSize: 28,
    fontWeight: '800',
    color: '#1E293B',
    letterSpacing: -0.5,
  },
  yearTitle: {
    fontSize: 14,
    fontWeight: '500',
    color: '#64748B',
    marginTop: 2,
  },
  calendarCard: {
    backgroundColor: '#FFFFFF',
    borderRadius: 24,
    paddingVertical: 24,
    paddingHorizontal: 20,
    shadowColor: '#64748B',
    shadowOffset: { width: 0, height: 8 },
    shadowOpacity: 0.1,
    shadowRadius: 24,
    elevation: 12,
  },
  dayHeaders: {
    flexDirection: 'row',
    marginBottom: 16,
    paddingHorizontal: 4,
  },
  dayHeader: {
    flex: 1,
    alignItems: 'center',
    paddingVertical: 8,
  },
  dayHeaderText: {
    fontSize: 12,
    fontWeight: '600',
    color: '#64748B',
    textTransform: 'uppercase',
    letterSpacing: 0.5,
  },
  calendarGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 8,
  },
  emptyDay: {
    width: (width - 88) / 7,
    height: 48,
  },
  calendarDay: {
    width: (width - 88) / 7,
    height: 48,
  },
  selectedDay: {
    transform: [{ scale: 1.05 }],
  },
  todayDay: {
    transform: [{ scale: 1.02 }],
  },
  dayContainer: {
    flex: 1,
    borderRadius: 12,
    backgroundColor: '#F8FAFC',
    justifyContent: 'center',
    alignItems: 'center',
    position: 'relative',
  },
  selectedDayContainer: {
    backgroundColor: '#6366F1',
    shadowColor: '#6366F1',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.3,
    shadowRadius: 8,
    elevation: 8,
  },
  todayDayContainer: {
    backgroundColor: '#F1F5F9',
    borderWidth: 2,
    borderColor: '#6366F1',
  },
  dayText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#475569',
  },
  selectedDayText: {
    color: '#FFFFFF',
    fontWeight: '700',
  },
  todayDayText: {
    color: '#6366F1',
    fontWeight: '700',
  },
  entriesIndicator: {
    position: 'absolute',
    bottom: 4,
    alignSelf: 'center',
  },
  entryDot: {
    width: 6,
    height: 6,
    borderRadius: 3,
    backgroundColor: '#10B981',
  },
  selectedEntryDot: {
    backgroundColor: '#FFFFFF',
  },
  todayEntryDot: {
    backgroundColor: '#6366F1',
  },
  // Modern Today Section Styles
  todaySection: {
    paddingHorizontal: 24,
    paddingTop: 16, // Reduced space between calendar and today section
    paddingBottom: 32,
  },
  todayCard: {
    backgroundColor: '#FFFFFF',
    borderRadius: 24,
    paddingVertical: 24,
    paddingHorizontal: 24,
    shadowColor: '#64748B',
    shadowOffset: { width: 0, height: 8 },
    shadowOpacity: 0.1,
    shadowRadius: 24,
    elevation: 12,
  },
  todayHeader: {
    marginBottom: 20,
  },
  todayTitleContainer: {
    alignItems: 'center',
  },
  todayTitle: {
    fontSize: 22,
    fontWeight: '700',
    color: '#1E293B',
    marginBottom: 4,
  },
  todayDate: {
    fontSize: 14,
    fontWeight: '500',
    color: '#64748B',
  },
  todayContent: {
    gap: 20,
  },
  statsRow: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    backgroundColor: '#F8FAFC',
    borderRadius: 16,
    paddingVertical: 16,
    paddingHorizontal: 12,
  },
  statItem: {
    alignItems: 'center',
  },
  statNumber: {
    fontSize: 24,
    fontWeight: '800',
    color: '#6366F1',
    marginBottom: 4,
  },
  statLabel: {
    fontSize: 12,
    fontWeight: '600',
    color: '#64748B',
    textTransform: 'uppercase',
    letterSpacing: 0.5,
  },
  viewDayButton: {
    borderRadius: 16,
    overflow: 'hidden',
    shadowColor: '#6366F1',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.2,
    shadowRadius: 12,
    elevation: 8,
  },
  viewDayGradient: {
    paddingVertical: 16,
    paddingHorizontal: 24,
    alignItems: 'center',
  },
  viewDayText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: '700',
  },
  emptyState: {
    alignItems: 'center',
    paddingVertical: 20,
  },
  emptyIcon: {
    width: 64,
    height: 64,
    borderRadius: 32,
    backgroundColor: '#F1F5F9',
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 16,
  },
  emptyIconText: {
    fontSize: 28,
  },
  emptyTitle: {
    fontSize: 18,
    fontWeight: '700',
    color: '#1E293B',
    marginBottom: 4,
  },
  emptySubtitle: {
    fontSize: 14,
    fontWeight: '500',
    color: '#64748B',
    marginBottom: 24,
  },
  addFirstButton: {
    borderRadius: 16,
    overflow: 'hidden',
    shadowColor: '#6366F1',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.2,
    shadowRadius: 12,
    elevation: 8,
  },
  addFirstGradient: {
    paddingVertical: 16,
    paddingHorizontal: 32,
    alignItems: 'center',
  },
  addFirstText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: '700',
  },
  statsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    gap: 16,
  },
  statCard: {
    flex: 1,
    alignItems: 'center',
    paddingVertical: 20,
    paddingHorizontal: 16,
  },
  statNumber: {
    fontSize: 28,
    fontWeight: 'bold',
    marginBottom: 6,
  },
  statLabel: {
    fontSize: 13,
    opacity: 0.7,
    fontWeight: '500',
  },
  searchContainer: {
    paddingHorizontal: 20,
    marginBottom: 24,
  },
  searchCard: {
    paddingVertical: 4,
    paddingHorizontal: 4,
  },
  searchInput: {
    fontSize: 17,
    paddingHorizontal: 16,
    paddingVertical: 12,
    backgroundColor: 'transparent',
    color: '#1D1D1F',
  },
  entriesContainer: {
    flex: 1,
  },
  entriesList: {
    paddingHorizontal: 20,
    paddingBottom: 20,
  },
  entryCard: {
    marginBottom: 20,
  },
  entryHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  entryDateContainer: {
    backgroundColor: 'rgba(102, 126, 234, 0.1)',
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 12,
  },
  entryDate: {
    fontSize: 13,
    fontWeight: '600',
    color: '#667eea',
  },
  moodContainer: {
    backgroundColor: 'rgba(255, 255, 255, 0.8)',
    width: 36,
    height: 36,
    borderRadius: 18,
    justifyContent: 'center',
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  moodEmoji: {
    fontSize: 20,
  },
  entryTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    marginBottom: 8,
  },
  entryPreview: {
    fontSize: 16,
    lineHeight: 22,
    opacity: 0.8,
    marginBottom: 12,
  },
  entryFooter: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  wordCount: {
    fontSize: 12,
    opacity: 0.6,
  },
  tagsContainer: {
    flexDirection: 'row',
    gap: 6,
  },
  tag: {
    backgroundColor: 'rgba(99, 102, 241, 0.1)',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 8,
  },
  tagText: {
    fontSize: 12,
    color: '#6366F1',
    fontWeight: '500',
  },
  emptyState: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 40,
  },
  emptyCard: {
    alignItems: 'center',
    paddingVertical: 40,
    paddingHorizontal: 32,
  },
  emptyStateImage: {
    width: 120,
    height: 120,
    marginBottom: 24,
    opacity: 0.6,
  },
  emptyStateText: {
    fontSize: 17,
    textAlign: 'center',
    opacity: 0.8,
    marginBottom: 32,
    lineHeight: 24,
  },
  startButton: {
    borderRadius: 16,
    overflow: 'hidden',
    shadowColor: '#667eea',
    shadowOffset: {
      width: 0,
      height: 4,
    },
    shadowOpacity: 0.3,
    shadowRadius: 8,
    elevation: 8,
  },
  startButtonGradient: {
    paddingHorizontal: 32,
    paddingVertical: 16,
    backgroundColor: '#667eea',
    alignItems: 'center',
  },
  startButtonText: {
    color: '#fff',
    fontSize: 17,
    fontWeight: '600',
    textAlign: 'center',
  },
  // Bottom Navigation Styles
  bottomNav: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    backgroundColor: '#FFFFFF',
    flexDirection: 'row',
    paddingVertical: 12,
    paddingHorizontal: 24,
    paddingBottom: 34, // Safe area for iPhone
    shadowColor: '#64748B',
    shadowOffset: { width: 0, height: -4 },
    shadowOpacity: 0.1,
    shadowRadius: 16,
    elevation: 16,
  },
  navItem: {
    flex: 1,
    alignItems: 'center',
    paddingVertical: 8,
  },
  navItemActive: {
    // Active state styling handled by text colors
  },
  navIcon: {
    fontSize: 20,
    marginBottom: 4,
  },
  navIconActive: {
    // Active icon styling
  },
  navLabel: {
    fontSize: 12,
    fontWeight: '600',
    color: '#64748B',
  },
  navLabelActive: {
    color: '#6366F1',
    fontWeight: '700',
  },
  navAddButton: {
    width: 48,
    height: 48,
    borderRadius: 24,
    overflow: 'hidden',
    shadowColor: '#6366F1',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.3,
    shadowRadius: 12,
    elevation: 8,
  },
  navAddGradient: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  navAddIcon: {
    fontSize: 24,
    fontWeight: '700',
    color: '#FFFFFF',
  },
});


import { LinearGradient } from 'expo-linear-gradient';
import { router } from 'expo-router';
import { StatusBar } from 'expo-status-bar';
import { useState } from 'react';
import { Dimensions, Pressable, StyleSheet, View } from 'react-native';

import { Card } from '@/components/Card';
import { GradientBackground } from '@/components/GradientBackground';
import { ThemedText } from '@/components/ThemedText';
import { useColorScheme } from '@/hooks/useColorScheme';

const { width } = Dimensions.get('window');

// Sample data for days with entries
const SAMPLE_ENTRIES = {
  '2024-01-15': [
    { time: '07:00', activity: 'Morning Routine', hasVoice: true, hasPhoto: false },
    { time: '08:00', activity: 'Breakfast', hasVoice: false, hasPhoto: true },
    { time: '09:00', activity: 'Work Session', hasVoice: true, hasPhoto: false },
  ],
  '2024-01-16': [
    { time: '06:30', activity: 'Workout', hasVoice: false, hasPhoto: true },
    { time: '08:30', activity: 'Breakfast', hasVoice: true, hasPhoto: true },
  ],
  '2024-01-17': [
    { time: '07:30', activity: 'Meditation', hasVoice: true, hasPhoto: false },
  ],
};

export default function CalendarHomeScreen() {
  const [selectedDate, setSelectedDate] = useState(new Date());
  const [currentMonth, setCurrentMonth] = useState(new Date());
  const colorScheme = useColorScheme();

  // Get calendar data
  const getDaysInMonth = (date: Date) => {
    const year = date.getFullYear();
    const month = date.getMonth();
    const firstDay = new Date(year, month, 1);
    const lastDay = new Date(year, month + 1, 0);
    const daysInMonth = lastDay.getDate();
    const startingDayOfWeek = firstDay.getDay();

    const days = [];

    // Add empty cells for days before the first day of the month
    for (let i = 0; i < startingDayOfWeek; i++) {
      days.push(null);
    }

    // Add all days of the month
    for (let day = 1; day <= daysInMonth; day++) {
      days.push(new Date(year, month, day));
    }

    return days;
  };

  const formatDateKey = (date: Date) => {
    return date.toISOString().split('T')[0];
  };

  const isToday = (date: Date) => {
    const today = new Date();
    return date.toDateString() === today.toDateString();
  };

  const isSameDay = (date1: Date, date2: Date) => {
    return date1.toDateString() === date2.toDateString();
  };

  const hasEntries = (date: Date) => {
    const dateKey = formatDateKey(date);
    return SAMPLE_ENTRIES[dateKey as keyof typeof SAMPLE_ENTRIES];
  };

  const handleDateSelect = (date: Date) => {
    setSelectedDate(date);
    const dateKey = formatDateKey(date);
    if (SAMPLE_ENTRIES[dateKey as keyof typeof SAMPLE_ENTRIES]) {
      router.push('/timeline');
    } else {
      router.push('/add-activity');
    }
  };

  const navigateMonth = (direction: 'prev' | 'next') => {
    const newMonth = new Date(currentMonth);
    if (direction === 'prev') {
      newMonth.setMonth(newMonth.getMonth() - 1);
    } else {
      newMonth.setMonth(newMonth.getMonth() + 1);
    }
    setCurrentMonth(newMonth);
  };

  const renderCalendarDay = (date: Date | null, index: number) => {
    if (!date) {
      return <View key={index} style={styles.emptyDay} />;
    }

    const entries = hasEntries(date);
    const isSelected = isSameDay(date, selectedDate);
    const todayDate = isToday(date);

    return (
      <Pressable
        key={index}
        style={[
          styles.calendarDay,
          isSelected && styles.selectedDay,
          todayDate && styles.todayDay,
        ]}
        onPress={() => handleDateSelect(date)}
      >
        <LinearGradient
          colors={
            isSelected
              ? ['#667eea', '#764ba2']
              : todayDate
              ? ['#f093fb', '#f5576c']
              : ['transparent', 'transparent']
          }
          style={styles.dayGradient}
        >
          <ThemedText
            style={[
              styles.dayText,
              (isSelected || todayDate) && styles.selectedDayText,
            ]}
          >
            {date.getDate()}
          </ThemedText>

          {entries && (
            <View style={styles.entriesIndicator}>
              <View style={styles.entryDot} />
              {entries.length > 1 && <View style={styles.entryDot} />}
              {entries.length > 2 && <View style={styles.entryDot} />}
            </View>
          )}
        </LinearGradient>
      </Pressable>
    );
  };

  const monthNames = [
    'January', 'February', 'March', 'April', 'May', 'June',
    'July', 'August', 'September', 'October', 'November', 'December'
  ];

  const dayNames = ['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'];

  return (
    <GradientBackground variant="primary">
      <StatusBar style={colorScheme === 'dark' ? 'light' : 'dark'} />

      {/* Header */}
      <View style={styles.header}>
        <View style={styles.headerTop}>
          <Pressable onPress={() => router.push('/stats')} style={styles.profileButton}>
            <LinearGradient colors={['#667eea', '#764ba2']} style={styles.profileGradient}>
              <ThemedText style={styles.profileText}>👤</ThemedText>
            </LinearGradient>
          </Pressable>
          <ThemedText style={styles.headerTitle}>My Daily Journal</ThemedText>
          <Pressable onPress={() => router.push('/add-activity')} style={styles.addButton}>
            <LinearGradient colors={['#f093fb', '#f5576c']} style={styles.addGradient}>
              <ThemedText style={styles.addText}>+</ThemedText>
            </LinearGradient>
          </Pressable>
        </View>
      </View>

      {/* Calendar */}
      <View style={styles.calendarContainer}>
        {/* Month Navigation */}
        <Card variant="ios" style={styles.monthCard}>
          <View style={styles.monthHeader}>
            <Pressable onPress={() => navigateMonth('prev')} style={styles.navButton}>
              <ThemedText style={styles.navText}>‹</ThemedText>
            </Pressable>
            <ThemedText style={styles.monthTitle}>
              {monthNames[currentMonth.getMonth()]} {currentMonth.getFullYear()}
            </ThemedText>
            <Pressable onPress={() => navigateMonth('next')} style={styles.navButton}>
              <ThemedText style={styles.navText}>›</ThemedText>
            </Pressable>
          </View>

          {/* Day Headers */}
          <View style={styles.dayHeaders}>
            {dayNames.map((day) => (
              <View key={day} style={styles.dayHeader}>
                <ThemedText style={styles.dayHeaderText}>{day}</ThemedText>
              </View>
            ))}
          </View>

          {/* Calendar Grid */}
          <View style={styles.calendarGrid}>
            {getDaysInMonth(currentMonth).map((date, index) => renderCalendarDay(date, index))}
          </View>
        </Card>
      </View>

      {/* Today's Quick Summary */}
      <View style={styles.todaySection}>
        <Card variant="ios" style={styles.todayCard}>
          <View style={styles.todayHeader}>
            <ThemedText style={styles.todayTitle}>Today's Summary</ThemedText>
            <ThemedText style={styles.todayDate}>
              {selectedDate.toLocaleDateString('en-US', {
                weekday: 'long',
                month: 'long',
                day: 'numeric'
              })}
            </ThemedText>
          </View>

          {hasEntries(selectedDate) ? (
            <View style={styles.todayEntries}>
              <ThemedText style={styles.entriesCount}>
                {hasEntries(selectedDate)?.length} activities logged
              </ThemedText>
              <Pressable
                onPress={() => handleDateSelect(selectedDate)}
                style={styles.viewDayButton}
              >
                <LinearGradient colors={['#667eea', '#764ba2']} style={styles.viewDayGradient}>
                  <ThemedText style={styles.viewDayText}>View Day</ThemedText>
                </LinearGradient>
              </Pressable>
            </View>
          ) : (
            <View style={styles.noEntries}>
              <ThemedText style={styles.noEntriesText}>No activities logged yet</ThemedText>
              <Pressable
                onPress={() => handleDateSelect(selectedDate)}
                style={styles.addFirstButton}
              >
                <LinearGradient colors={['#f093fb', '#f5576c']} style={styles.addFirstGradient}>
                  <ThemedText style={styles.addFirstText}>Add First Activity</ThemedText>
                </LinearGradient>
              </Pressable>
            </View>
          )}
        </Card>
      </View>
    </GradientBackground>
  );
}

const styles = StyleSheet.create({
  header: {
    paddingTop: 60,
    paddingHorizontal: 20,
    paddingBottom: 20,
  },
  headerTop: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  headerTitle: {
    fontSize: 28,
    fontWeight: 'bold',
    flex: 1,
    textAlign: 'center',
    color: '#FFFFFF',
  },
  profileButton: {
    width: 44,
    height: 44,
    borderRadius: 22,
    overflow: 'hidden',
  },
  profileGradient: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  profileText: {
    fontSize: 20,
    color: '#FFFFFF',
  },
  addButton: {
    width: 44,
    height: 44,
    borderRadius: 22,
    overflow: 'hidden',
  },
  addGradient: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  addText: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#FFFFFF',
  },
  calendarContainer: {
    flex: 1,
    paddingHorizontal: 20,
  },
  monthCard: {
    paddingVertical: 20,
    paddingHorizontal: 20,
    marginBottom: 20,
  },
  monthHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 20,
  },
  navButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: 'rgba(102, 126, 234, 0.1)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  navText: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#667eea',
  },
  monthTitle: {
    fontSize: 22,
    fontWeight: 'bold',
  },
  dayHeaders: {
    flexDirection: 'row',
    marginBottom: 10,
  },
  dayHeader: {
    flex: 1,
    alignItems: 'center',
    paddingVertical: 10,
  },
  dayHeaderText: {
    fontSize: 14,
    fontWeight: '600',
    opacity: 0.7,
  },
  calendarGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
  },
  emptyDay: {
    width: width / 7 - 4,
    height: 60,
    margin: 2,
  },
  calendarDay: {
    width: width / 7 - 4,
    height: 60,
    margin: 2,
    borderRadius: 12,
    overflow: 'hidden',
  },
  selectedDay: {
    shadowColor: '#667eea',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.3,
    shadowRadius: 8,
    elevation: 8,
  },
  todayDay: {
    shadowColor: '#f093fb',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.3,
    shadowRadius: 8,
    elevation: 8,
  },
  dayGradient: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(255, 255, 255, 0.8)',
  },
  dayText: {
    fontSize: 16,
    fontWeight: '600',
  },
  selectedDayText: {
    color: '#FFFFFF',
    fontWeight: 'bold',
  },
  entriesIndicator: {
    flexDirection: 'row',
    position: 'absolute',
    bottom: 4,
    alignSelf: 'center',
    gap: 2,
  },
  entryDot: {
    width: 4,
    height: 4,
    borderRadius: 2,
    backgroundColor: '#FFFFFF',
  },
  todaySection: {
    paddingHorizontal: 20,
    paddingBottom: 20,
  },
  todayCard: {
    paddingVertical: 20,
    paddingHorizontal: 20,
  },
  todayHeader: {
    marginBottom: 16,
  },
  todayTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    marginBottom: 4,
  },
  todayDate: {
    fontSize: 16,
    opacity: 0.7,
  },
  todayEntries: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  entriesCount: {
    fontSize: 16,
    fontWeight: '500',
  },
  viewDayButton: {
    borderRadius: 12,
    overflow: 'hidden',
  },
  viewDayGradient: {
    paddingHorizontal: 16,
    paddingVertical: 8,
  },
  viewDayText: {
    color: '#FFFFFF',
    fontSize: 14,
    fontWeight: '600',
  },
  noEntries: {
    alignItems: 'center',
  },
  noEntriesText: {
    fontSize: 16,
    opacity: 0.7,
    marginBottom: 16,
  },
  addFirstButton: {
    borderRadius: 12,
    overflow: 'hidden',
  },
  addFirstGradient: {
    paddingHorizontal: 20,
    paddingVertical: 12,
  },
  addFirstText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: '600',
  },
  statsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    gap: 16,
  },
  statCard: {
    flex: 1,
    alignItems: 'center',
    paddingVertical: 20,
    paddingHorizontal: 16,
  },
  statNumber: {
    fontSize: 28,
    fontWeight: 'bold',
    marginBottom: 6,
  },
  statLabel: {
    fontSize: 13,
    opacity: 0.7,
    fontWeight: '500',
  },
  searchContainer: {
    paddingHorizontal: 20,
    marginBottom: 24,
  },
  searchCard: {
    paddingVertical: 4,
    paddingHorizontal: 4,
  },
  searchInput: {
    fontSize: 17,
    paddingHorizontal: 16,
    paddingVertical: 12,
    backgroundColor: 'transparent',
    color: '#1D1D1F',
  },
  entriesContainer: {
    flex: 1,
  },
  entriesList: {
    paddingHorizontal: 20,
    paddingBottom: 20,
  },
  entryCard: {
    marginBottom: 20,
  },
  entryHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  entryDateContainer: {
    backgroundColor: 'rgba(102, 126, 234, 0.1)',
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 12,
  },
  entryDate: {
    fontSize: 13,
    fontWeight: '600',
    color: '#667eea',
  },
  moodContainer: {
    backgroundColor: 'rgba(255, 255, 255, 0.8)',
    width: 36,
    height: 36,
    borderRadius: 18,
    justifyContent: 'center',
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  moodEmoji: {
    fontSize: 20,
  },
  entryTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    marginBottom: 8,
  },
  entryPreview: {
    fontSize: 16,
    lineHeight: 22,
    opacity: 0.8,
    marginBottom: 12,
  },
  entryFooter: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  wordCount: {
    fontSize: 12,
    opacity: 0.6,
  },
  tagsContainer: {
    flexDirection: 'row',
    gap: 6,
  },
  tag: {
    backgroundColor: 'rgba(99, 102, 241, 0.1)',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 8,
  },
  tagText: {
    fontSize: 12,
    color: '#6366F1',
    fontWeight: '500',
  },
  emptyState: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 40,
  },
  emptyCard: {
    alignItems: 'center',
    paddingVertical: 40,
    paddingHorizontal: 32,
  },
  emptyStateImage: {
    width: 120,
    height: 120,
    marginBottom: 24,
    opacity: 0.6,
  },
  emptyStateText: {
    fontSize: 17,
    textAlign: 'center',
    opacity: 0.8,
    marginBottom: 32,
    lineHeight: 24,
  },
  startButton: {
    borderRadius: 16,
    overflow: 'hidden',
    shadowColor: '#667eea',
    shadowOffset: {
      width: 0,
      height: 4,
    },
    shadowOpacity: 0.3,
    shadowRadius: 8,
    elevation: 8,
  },
  startButtonGradient: {
    paddingHorizontal: 32,
    paddingVertical: 16,
    backgroundColor: '#667eea',
    alignItems: 'center',
  },
  startButtonText: {
    color: '#fff',
    fontSize: 17,
    fontWeight: '600',
    textAlign: 'center',
  },
});


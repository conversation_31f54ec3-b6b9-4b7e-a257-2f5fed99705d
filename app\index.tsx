import { Image } from 'expo-image';
import { router } from 'expo-router';
import { StatusBar } from 'expo-status-bar';
import { useCallback, useEffect, useState } from 'react';
import { FlatList, Pressable, StyleSheet, TextInput, View } from 'react-native';

import { ThemedText } from '@/components/ThemedText';
import { ThemedView } from '@/components/ThemedView';
import { useJournal } from '@/context/JournalContext';

// Mood emojis for quick display
const MOOD_EMOJIS = {
  'happy': '😊',
  'sad': '😔',
  'excited': '🤩',
  'anxious': '😰',
  'calm': '😌',
};

export default function JournalHomeScreen() {
  const { entries, loading, getWritingStreak, getTotalWordCount } = useJournal();
  const [searchQuery, setSearchQuery] = useState('');
  const [filteredEntries, setFilteredEntries] = useState(entries);

  useEffect(() => {
    if (searchQuery.trim()) {
      const filtered = entries.filter(entry =>
        entry.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
        entry.content.toLowerCase().includes(searchQuery.toLowerCase()) ||
        entry.tags?.some(tag => tag.toLowerCase().includes(searchQuery.toLowerCase()))
      );
      setFilteredEntries(filtered);
    } else {
      setFilteredEntries(entries);
    }
  }, [searchQuery, entries]);

  const handleNewEntry = useCallback(() => {
    router.push('/new-entry');
  }, []);

  const renderJournalEntry = ({ item }: { item: typeof entries[0] }) => (
    <Pressable
      style={styles.entryCard}
      onPress={() => router.push(`/entry/${item.id}`)}
    >
      <View style={styles.entryHeader}>
        <ThemedText style={styles.entryDate}>
          {new Date(item.createdAt || item.date).toLocaleDateString('en-US', {
            weekday: 'short',
            month: 'short',
            day: 'numeric'
          })}
        </ThemedText>
        {item.mood && (
          <ThemedText style={styles.moodEmoji}>
            {MOOD_EMOJIS[item.mood as keyof typeof MOOD_EMOJIS]}
          </ThemedText>
        )}
      </View>
      <ThemedText style={styles.entryTitle}>{item.title}</ThemedText>
      <ThemedText numberOfLines={2} style={styles.entryPreview}>
        {item.preview}
      </ThemedText>
      <View style={styles.entryFooter}>
        <ThemedText style={styles.wordCount}>
          {item.wordCount || 0} words • {item.readingTime || 1} min read
        </ThemedText>
        {item.tags && item.tags.length > 0 && (
          <View style={styles.tagsContainer}>
            {item.tags.slice(0, 2).map((tag, index) => (
              <View key={index} style={styles.tag}>
                <ThemedText style={styles.tagText}>#{tag}</ThemedText>
              </View>
            ))}
          </View>
        )}
      </View>
    </Pressable>
  );

  if (loading) {
    return (
      <ThemedView style={styles.container}>
        <View style={styles.loadingContainer}>
          <ThemedText>Loading your journal...</ThemedText>
        </View>
      </ThemedView>
    );
  }

  return (
    <ThemedView style={styles.container}>
      <StatusBar style="auto" />

      {/* Header with stats */}
      <View style={styles.header}>
        <View style={styles.headerTop}>
          <ThemedText style={styles.headerTitle}>My Journal</ThemedText>
          <Pressable style={styles.newButton} onPress={handleNewEntry}>
            <ThemedText style={styles.newButtonText}>+</ThemedText>
          </Pressable>
        </View>

        {/* Quick stats */}
        <View style={styles.statsContainer}>
          <View style={styles.statCard}>
            <ThemedText style={styles.statNumber}>{entries.length}</ThemedText>
            <ThemedText style={styles.statLabel}>Entries</ThemedText>
          </View>
          <View style={styles.statCard}>
            <ThemedText style={styles.statNumber}>{getWritingStreak()}</ThemedText>
            <ThemedText style={styles.statLabel}>Day Streak</ThemedText>
          </View>
          <View style={styles.statCard}>
            <ThemedText style={styles.statNumber}>{Math.round(getTotalWordCount() / 1000)}k</ThemedText>
            <ThemedText style={styles.statLabel}>Words</ThemedText>
          </View>
        </View>
      </View>

      {/* Search bar */}
      <View style={styles.searchContainer}>
        <TextInput
          style={styles.searchInput}
          placeholder="Search your thoughts..."
          placeholderTextColor="#999"
          value={searchQuery}
          onChangeText={setSearchQuery}
        />
      </View>

      {/* Entries list */}
      {filteredEntries.length > 0 ? (
        <View style={styles.entriesContainer}>
          <FlatList
            data={filteredEntries}
            renderItem={renderJournalEntry}
            keyExtractor={item => item.id}
            contentContainerStyle={styles.entriesList}
            showsVerticalScrollIndicator={false}
          />
        </View>
      ) : (
        <View style={styles.emptyState}>
          <Image
            source={require('@/assets/images/icon.png')}
            style={styles.emptyStateImage}
          />
          <ThemedText style={styles.emptyStateText}>
            {searchQuery ? 'No entries found for your search.' : 'No journal entries yet. Tap + to create your first entry.'}
          </ThemedText>
          {!searchQuery && (
            <Pressable style={styles.startButton} onPress={handleNewEntry}>
              <ThemedText style={styles.startButtonText}>Start Writing</ThemedText>
            </Pressable>
          )}
        </View>
      )}
    </ThemedView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#fff',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  header: {
    paddingTop: 60,
    paddingHorizontal: 20,
    paddingBottom: 20,
  },
  headerTop: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 20,
  },
  headerTitle: {
    fontSize: 32,
    fontWeight: 'bold',
  },
  newButton: {
    width: 44,
    height: 44,
    borderRadius: 22,
    backgroundColor: '#6366F1',
    justifyContent: 'center',
    alignItems: 'center',
  },
  newButtonText: {
    color: '#fff',
    fontSize: 24,
    fontWeight: 'bold',
  },
  statsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    gap: 12,
  },
  statCard: {
    flex: 1,
    alignItems: 'center',
    paddingVertical: 16,
    paddingHorizontal: 12,
  },
  statNumber: {
    fontSize: 24,
    fontWeight: 'bold',
    marginBottom: 4,
  },
  statLabel: {
    fontSize: 12,
    opacity: 0.7,
  },
  searchContainer: {
    paddingHorizontal: 20,
    marginBottom: 20,
  },
  searchInput: {
    backgroundColor: 'rgba(255, 255, 255, 0.9)',
    borderRadius: 12,
    paddingHorizontal: 16,
    paddingVertical: 12,
    fontSize: 16,
    borderWidth: 1,
    borderColor: 'rgba(0, 0, 0, 0.1)',
  },
  entriesContainer: {
    flex: 1,
  },
  entriesList: {
    paddingHorizontal: 20,
    paddingBottom: 20,
  },
  entryCard: {
    marginBottom: 16,
  },
  entryHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  entryDate: {
    fontSize: 14,
    opacity: 0.7,
  },
  moodEmoji: {
    fontSize: 20,
  },
  entryTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    marginBottom: 8,
  },
  entryPreview: {
    fontSize: 16,
    lineHeight: 22,
    opacity: 0.8,
    marginBottom: 12,
  },
  entryFooter: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  wordCount: {
    fontSize: 12,
    opacity: 0.6,
  },
  tagsContainer: {
    flexDirection: 'row',
    gap: 6,
  },
  tag: {
    backgroundColor: 'rgba(99, 102, 241, 0.1)',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 8,
  },
  tagText: {
    fontSize: 12,
    color: '#6366F1',
    fontWeight: '500',
  },
  emptyState: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 40,
  },
  emptyStateImage: {
    width: 120,
    height: 120,
    marginBottom: 20,
    opacity: 0.5,
  },
  emptyStateText: {
    fontSize: 16,
    textAlign: 'center',
    opacity: 0.7,
    marginBottom: 20,
  },
  startButton: {
    paddingHorizontal: 32,
    paddingVertical: 12,
    backgroundColor: '#6366F1',
    borderRadius: 12,
    marginTop: 20,
  },
  startButtonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: 'bold',
    textAlign: 'center',
  },
});


import { Image } from 'expo-image';
import { router } from 'expo-router';
import { StatusBar } from 'expo-status-bar';
import { useCallback } from 'react';
import { FlatList, Pressable, StyleSheet } from 'react-native';

import { ThemedText } from '@/components/ThemedText';
import { ThemedView } from '@/components/ThemedView';

// Sample journal entries for demonstration
const SAMPLE_ENTRIES = [
  { id: '1', date: 'May 15, 2023', title: 'Morning Reflections', preview: 'Today started with a beautiful sunrise...' },
  { id: '2', date: 'May 14, 2023', title: 'New Project Ideas', preview: 'I have been thinking about starting a side project...' },
  { id: '3', date: 'May 12, 2023', title: 'Weekend Plans', preview: 'Looking forward to hiking this weekend...' },
];

export default function JournalHomeScreen() {
  const handleNewEntry = useCallback(() => {
    router.push('/new-entry');
  }, []);

  const renderJournalEntry = ({ item }: { item: typeof SAMPLE_ENTRIES[0] }) => (
    <Pressable 
      style={styles.entryCard}
      onPress={() => router.push(`/entry/${item.id}`)}
    >
      <ThemedText style={styles.entryDate}>{item.date}</ThemedText>
      <ThemedText style={styles.entryTitle}>{item.title}</ThemedText>
      <ThemedText numberOfLines={2} style={styles.entryPreview}>{item.preview}</ThemedText>
    </Pressable>
  );

  return (
    <ThemedView style={styles.container}>
      <StatusBar style="auto" />
      
      <ThemedView style={styles.header}>
        <ThemedText style={styles.headerTitle}>My Journal</ThemedText>
        <Pressable style={styles.newButton} onPress={handleNewEntry}>
          <ThemedText style={styles.newButtonText}>+</ThemedText>
        </Pressable>
      </ThemedView>
      
      {SAMPLE_ENTRIES.length > 0 ? (
        <FlatList
          data={SAMPLE_ENTRIES}
          renderItem={renderJournalEntry}
          keyExtractor={item => item.id}
          contentContainerStyle={styles.entriesList}
        />
      ) : (
        <ThemedView style={styles.emptyState}>
          <Image
            source={require('@/assets/images/icon.png')}
            style={styles.emptyStateImage}
          />
          <ThemedText style={styles.emptyStateText}>
            No journal entries yet. Tap + to create your first entry.
          </ThemedText>
        </ThemedView>
      )}
    </ThemedView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    paddingTop: 60,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 20,
    marginBottom: 20,
  },
  headerTitle: {
    fontSize: 28,
    fontWeight: 'bold',
  },
  newButton: {
    width: 50,
    height: 50,
    borderRadius: 25,
    backgroundColor: '#A1CEDC',
    justifyContent: 'center',
    alignItems: 'center',
  },
  newButtonText: {
    fontSize: 30,
    fontWeight: 'bold',
    color: '#fff',
  },
  entriesList: {
    padding: 20,
  },
  entryCard: {
    padding: 20,
    marginBottom: 15,
    borderRadius: 12,
    backgroundColor: '#f8f8f8',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  entryDate: {
    fontSize: 14,
    color: '#888',
    marginBottom: 6,
  },
  entryTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 8,
  },
  entryPreview: {
    fontSize: 16,
    color: '#555',
  },
  emptyState: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 40,
  },
  emptyStateImage: {
    width: 120,
    height: 120,
    marginBottom: 20,
  },
  emptyStateText: {
    fontSize: 16,
    textAlign: 'center',
    color: '#888',
  },
});


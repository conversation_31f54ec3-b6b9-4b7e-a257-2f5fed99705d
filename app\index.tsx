import { Image } from 'expo-image';
import { router } from 'expo-router';
import { StatusBar } from 'expo-status-bar';
import { useCallback, useEffect, useState } from 'react';
import { FlatList, Pressable, StyleSheet, TextInput, View } from 'react-native';

import { ThemedText } from '@/components/ThemedText';
import { useJournal } from '@/context/JournalContext';

// Mood emojis for quick display
const MOOD_EMOJIS = {
  'happy': '😊',
  'sad': '😔',
  'excited': '🤩',
  'anxious': '😰',
  'calm': '😌',
};

const { width } = require('react-native').Dimensions.get('window');

export default function JournalHomeScreen() {
  const { entries, loading, getWritingStreak, getTotalWordCount } = useJournal();
  const [searchQuery, setSearchQuery] = useState('');
  const [filteredEntries, setFilteredEntries] = useState(entries);
  const colorScheme = require('@/hooks/useColorScheme').useColorScheme();

  useEffect(() => {
    if (searchQuery.trim()) {
      const filtered = entries.filter(entry =>
        entry.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
        entry.content.toLowerCase().includes(searchQuery.toLowerCase()) ||
        entry.tags?.some(tag => tag.toLowerCase().includes(searchQuery.toLowerCase()))
      );
      setFilteredEntries(filtered);
    } else {
      setFilteredEntries(entries);
    }
  }, [searchQuery, entries]);

  const handleNewEntry = useCallback(() => {
    router.push('/new-entry');
  }, []);

  const GradientBackground = require('@/components/GradientBackground').GradientBackground;
  const Card = require('@/components/Card').Card;

  const renderJournalEntry = ({ item, index }: { item: typeof entries[0], index: number }) => {
    const gradients = [
      ['#667eea', '#764ba2'],
      ['#f093fb', '#f5576c'],
      ['#4facfe', '#00f2fe'],
      ['#43e97b', '#38f9d7'],
      ['#fa709a', '#fee140'],
      ['#a8edea', '#fed6e3'],
    ];

    return (
      <Card
        variant="ios"
        style={styles.entryCard}
        onPress={() => router.push(`/entry/${item.id}`)}
      >
        <View style={styles.entryHeader}>
          <View style={styles.entryDateContainer}>
            <ThemedText style={styles.entryDate}>
              {new Date(item.createdAt || item.date).toLocaleDateString('en-US', {
                weekday: 'short',
                month: 'short',
                day: 'numeric'
              })}
            </ThemedText>
          </View>
          {item.mood && (
            <View style={styles.moodContainer}>
              <ThemedText style={styles.moodEmoji}>
                {MOOD_EMOJIS[item.mood as keyof typeof MOOD_EMOJIS]}
              </ThemedText>
            </View>
          )}
        </View>

        <ThemedText style={styles.entryTitle}>{item.title}</ThemedText>
        <ThemedText numberOfLines={3} style={styles.entryPreview}>
          {item.preview}
        </ThemedText>

        <View style={styles.entryFooter}>
          <View style={styles.statsContainer}>
            <ThemedText style={styles.wordCount}>
              {item.wordCount || 0} words • {item.readingTime || 1} min read
            </ThemedText>
          </View>
          {item.tags && item.tags.length > 0 && (
            <View style={styles.tagsContainer}>
              {item.tags.slice(0, 2).map((tag, tagIndex) => (
                <View key={tagIndex} style={[styles.tag, { backgroundColor: gradients[index % gradients.length][0] + '20' }]}>
                  <ThemedText style={[styles.tagText, { color: gradients[index % gradients.length][0] }]}>
                    #{tag}
                  </ThemedText>
                </View>
              ))}
            </View>
          )}
        </View>
      </Card>
    );
  };

  if (loading) {
    return (
      <GradientBackground variant="ios">
        <View style={styles.loadingContainer}>
          <ThemedText style={styles.loadingText}>Loading your journal...</ThemedText>
        </View>
      </GradientBackground>
    );
  }

  return (
    <GradientBackground variant="ios">
      <StatusBar style={colorScheme === 'dark' ? 'light' : 'dark'} />

      {/* Header with stats */}
      <View style={styles.header}>
        <View style={styles.headerTop}>
          <Pressable onPress={() => router.push('/stats')} style={styles.statsButton}>
            <ThemedText style={styles.statsButtonText}>📊</ThemedText>
          </Pressable>
          <ThemedText style={styles.headerTitle}>My Journal</ThemedText>
          <Pressable style={styles.newButton} onPress={handleNewEntry}>
            <View style={styles.newButtonGradient}>
              <ThemedText style={styles.newButtonText}>+</ThemedText>
            </View>
          </Pressable>
        </View>

        {/* Quick stats */}
        <View style={styles.statsContainer}>
          <Card variant="ios" style={styles.statCard}>
            <ThemedText style={styles.statNumber}>{entries.length}</ThemedText>
            <ThemedText style={styles.statLabel}>Entries</ThemedText>
          </Card>
          <Card variant="ios" style={styles.statCard}>
            <ThemedText style={styles.statNumber}>{getWritingStreak()}</ThemedText>
            <ThemedText style={styles.statLabel}>Day Streak</ThemedText>
          </Card>
          <Card variant="ios" style={styles.statCard}>
            <ThemedText style={styles.statNumber}>{Math.round(getTotalWordCount() / 1000)}k</ThemedText>
            <ThemedText style={styles.statLabel}>Words</ThemedText>
          </Card>
        </View>
      </View>

      {/* Search bar */}
      <View style={styles.searchContainer}>
        <Card variant="ios" style={styles.searchCard}>
          <TextInput
            style={styles.searchInput}
            placeholder="Search your thoughts..."
            placeholderTextColor={colorScheme === 'dark' ? '#8E8E93' : '#8E8E93'}
            value={searchQuery}
            onChangeText={setSearchQuery}
          />
        </Card>
      </View>

      {/* Entries list */}
      {filteredEntries.length > 0 ? (
        <View style={styles.entriesContainer}>
          <FlatList
            data={filteredEntries}
            renderItem={renderJournalEntry}
            keyExtractor={item => item.id}
            contentContainerStyle={styles.entriesList}
            showsVerticalScrollIndicator={false}
          />
        </View>
      ) : (
        <View style={styles.emptyState}>
          <Card variant="ios" style={styles.emptyCard}>
            <Image
              source={require('@/assets/images/icon.png')}
              style={styles.emptyStateImage}
            />
            <ThemedText style={styles.emptyStateText}>
              {searchQuery ? 'No entries found for your search.' : 'No journal entries yet. Tap + to create your first entry.'}
            </ThemedText>
            {!searchQuery && (
              <Pressable style={styles.startButton} onPress={handleNewEntry}>
                <View style={styles.startButtonGradient}>
                  <ThemedText style={styles.startButtonText}>Start Writing</ThemedText>
                </View>
              </Pressable>
            )}
          </Card>
        </View>
      )}
    </GradientBackground>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#fff',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    fontSize: 18,
    fontWeight: '500',
    opacity: 0.7,
  },
  header: {
    paddingTop: 60,
    paddingHorizontal: 20,
    paddingBottom: 20,
  },
  headerTop: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 24,
  },
  headerTitle: {
    fontSize: 34,
    fontWeight: 'bold',
    letterSpacing: -0.5,
    flex: 1,
    textAlign: 'center',
  },
  statsButton: {
    width: 44,
    height: 44,
    borderRadius: 22,
    backgroundColor: 'rgba(102, 126, 234, 0.1)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  statsButtonText: {
    fontSize: 20,
  },
  newButton: {
    width: 50,
    height: 50,
    borderRadius: 25,
    overflow: 'hidden',
    shadowColor: '#667eea',
    shadowOffset: {
      width: 0,
      height: 4,
    },
    shadowOpacity: 0.3,
    shadowRadius: 8,
    elevation: 8,
  },
  newButtonGradient: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#667eea',
  },
  newButtonText: {
    color: '#fff',
    fontSize: 28,
    fontWeight: 'bold',
  },
  statsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    gap: 16,
  },
  statCard: {
    flex: 1,
    alignItems: 'center',
    paddingVertical: 20,
    paddingHorizontal: 16,
  },
  statNumber: {
    fontSize: 28,
    fontWeight: 'bold',
    marginBottom: 6,
  },
  statLabel: {
    fontSize: 13,
    opacity: 0.7,
    fontWeight: '500',
  },
  searchContainer: {
    paddingHorizontal: 20,
    marginBottom: 24,
  },
  searchCard: {
    paddingVertical: 4,
    paddingHorizontal: 4,
  },
  searchInput: {
    fontSize: 17,
    paddingHorizontal: 16,
    paddingVertical: 12,
    backgroundColor: 'transparent',
    color: '#1D1D1F',
  },
  entriesContainer: {
    flex: 1,
  },
  entriesList: {
    paddingHorizontal: 20,
    paddingBottom: 20,
  },
  entryCard: {
    marginBottom: 20,
  },
  entryHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  entryDateContainer: {
    backgroundColor: 'rgba(102, 126, 234, 0.1)',
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 12,
  },
  entryDate: {
    fontSize: 13,
    fontWeight: '600',
    color: '#667eea',
  },
  moodContainer: {
    backgroundColor: 'rgba(255, 255, 255, 0.8)',
    width: 36,
    height: 36,
    borderRadius: 18,
    justifyContent: 'center',
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  moodEmoji: {
    fontSize: 20,
  },
  entryTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    marginBottom: 8,
  },
  entryPreview: {
    fontSize: 16,
    lineHeight: 22,
    opacity: 0.8,
    marginBottom: 12,
  },
  entryFooter: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  wordCount: {
    fontSize: 12,
    opacity: 0.6,
  },
  tagsContainer: {
    flexDirection: 'row',
    gap: 6,
  },
  tag: {
    backgroundColor: 'rgba(99, 102, 241, 0.1)',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 8,
  },
  tagText: {
    fontSize: 12,
    color: '#6366F1',
    fontWeight: '500',
  },
  emptyState: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 40,
  },
  emptyCard: {
    alignItems: 'center',
    paddingVertical: 40,
    paddingHorizontal: 32,
  },
  emptyStateImage: {
    width: 120,
    height: 120,
    marginBottom: 24,
    opacity: 0.6,
  },
  emptyStateText: {
    fontSize: 17,
    textAlign: 'center',
    opacity: 0.8,
    marginBottom: 32,
    lineHeight: 24,
  },
  startButton: {
    borderRadius: 16,
    overflow: 'hidden',
    shadowColor: '#667eea',
    shadowOffset: {
      width: 0,
      height: 4,
    },
    shadowOpacity: 0.3,
    shadowRadius: 8,
    elevation: 8,
  },
  startButtonGradient: {
    paddingHorizontal: 32,
    paddingVertical: 16,
    backgroundColor: '#667eea',
    alignItems: 'center',
  },
  startButtonText: {
    color: '#fff',
    fontSize: 17,
    fontWeight: '600',
    textAlign: 'center',
  },
});


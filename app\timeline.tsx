import { router } from 'expo-router';
import { StatusBar } from 'expo-status-bar';
import React, { useState } from 'react';
import { Dimensions, Pressable, ScrollView, StyleSheet, Text, View } from 'react-native';

import { LinearGradient } from 'expo-linear-gradient';

import { Card } from '@/components/Card';
import { GradientBackground } from '@/components/GradientBackground';
import { ThemedText } from '@/components/ThemedText';
import { useColorScheme } from '@/hooks/useColorScheme';

const { width, height } = Dimensions.get('window');

// Category colors and icons
const CATEGORIES = {
  work: { color: '#3B82F6', icon: '💼', gradient: ['#3B82F6', '#1D4ED8'] },
  personal: { color: '#10B981', icon: '🏠', gradient: ['#10B981', '#059669'] },
  health: { color: '#F59E0B', icon: '🏃‍♂️', gradient: ['#F59E0B', '#D97706'] },
  social: { color: '#EF4444', icon: '👥', gradient: ['#EF4444', '#DC2626'] },
  entertainment: { color: '#8B5CF6', icon: '🎬', gradient: ['#8B5CF6', '#7C3AED'] },
  travel: { color: '#06B6D4', icon: '✈️', gradient: ['#06B6D4', '#0891B2'] },
  food: { color: '#F97316', icon: '🍽️', gradient: ['#F97316', '#EA580C'] },
  exercise: { color: '#84CC16', icon: '💪', gradient: ['#84CC16', '#65A30D'] },
  sleep: { color: '#6366F1', icon: '😴', gradient: ['#6366F1', '#4F46E5'] },
  other: { color: '#6B7280', icon: '📝', gradient: ['#6B7280', '#4B5563'] },
};

// Sample timeline data for today
const SAMPLE_TIMELINE = [
  {
    id: '1',
    startTime: '07:00',
    endTime: '08:00',
    activity: 'Morning Routine',
    category: 'personal',
    description: 'Meditation, shower, and breakfast',
    mood: 'good',
    energy: 'high',
    productivity: 4,
  },
  {
    id: '2',
    startTime: '09:00',
    endTime: '12:00',
    activity: 'Deep Work Session',
    category: 'work',
    description: 'Working on the new project features',
    mood: 'excellent',
    energy: 'high',
    productivity: 5,
  },
  {
    id: '3',
    startTime: '12:00',
    endTime: '13:00',
    activity: 'Lunch Break',
    category: 'food',
    description: 'Healthy salad and some reading',
    mood: 'good',
    energy: 'medium',
    productivity: 3,
  },
  {
    id: '4',
    startTime: '14:00',
    endTime: '17:00',
    activity: 'Team Meetings',
    category: 'work',
    description: 'Sprint planning and code reviews',
    mood: 'neutral',
    energy: 'medium',
    productivity: 3,
  },
  {
    id: '5',
    startTime: '18:00',
    endTime: '19:30',
    activity: 'Gym Workout',
    category: 'exercise',
    description: 'Strength training and cardio',
    mood: 'excellent',
    energy: 'high',
    productivity: 5,
  },
];

export default function TimelineScreen() {
  const [selectedDate, setSelectedDate] = useState(new Date());
  const [timeline, setTimeline] = useState(SAMPLE_TIMELINE);
  const colorScheme = useColorScheme();

  const formatDate = (date: Date) => {
    return date.toLocaleDateString('en-US', { 
      weekday: 'long', 
      year: 'numeric', 
      month: 'long', 
      day: 'numeric' 
    });
  };

  const getTotalHours = () => {
    return timeline.reduce((total, activity) => {
      const start = new Date(`2000-01-01 ${activity.startTime}`);
      const end = new Date(`2000-01-01 ${activity.endTime}`);
      return total + (end.getTime() - start.getTime()) / (1000 * 60 * 60);
    }, 0);
  };

  const getProductiveHours = () => {
    return timeline
      .filter(activity => activity.productivity >= 4)
      .reduce((total, activity) => {
        const start = new Date(`2000-01-01 ${activity.startTime}`);
        const end = new Date(`2000-01-01 ${activity.endTime}`);
        return total + (end.getTime() - start.getTime()) / (1000 * 60 * 60);
      }, 0);
  };

  const renderTimelineItem = (activity: any, index: number) => {
    const category = CATEGORIES[activity.category as keyof typeof CATEGORIES];

    return (
      <View
        key={activity.id}
        style={styles.timelineItem}
      >
        <View style={styles.timelineLeft}>
          <View style={styles.timeContainer}>
            <ThemedText style={styles.timeText}>{activity.startTime}</ThemedText>
            <View style={styles.timeDivider} />
            <ThemedText style={styles.timeText}>{activity.endTime}</ThemedText>
          </View>
          <View style={[styles.timelineDot, { backgroundColor: category.color }]} />
          <View style={styles.timelineLine} />
        </View>
        
        <Card variant="ios" style={styles.activityCard}>
          <LinearGradient
            colors={[category.color + '10', category.color + '05']}
            style={styles.activityGradient}
          >
            <View style={styles.activityHeader}>
              <View style={styles.activityTitleContainer}>
                <Text style={styles.categoryIcon}>{category.icon}</Text>
                <ThemedText style={styles.activityTitle}>{activity.activity}</ThemedText>
              </View>
              <View style={styles.moodContainer}>
                <ThemedText style={styles.moodText}>
                  {activity.mood === 'excellent' ? '😄' : 
                   activity.mood === 'good' ? '😊' : 
                   activity.mood === 'neutral' ? '😐' : 
                   activity.mood === 'bad' ? '😔' : '😢'}
                </ThemedText>
              </View>
            </View>
            
            <ThemedText style={styles.activityDescription}>
              {activity.description}
            </ThemedText>
            
            <View style={styles.activityFooter}>
              <View style={styles.energyContainer}>
                <ThemedText style={styles.energyLabel}>Energy:</ThemedText>
                <View style={styles.energyBars}>
                  {[1, 2, 3].map((bar) => (
                    <View
                      key={bar}
                      style={[
                        styles.energyBar,
                        {
                          backgroundColor: 
                            (activity.energy === 'high' && bar <= 3) ||
                            (activity.energy === 'medium' && bar <= 2) ||
                            (activity.energy === 'low' && bar <= 1)
                              ? category.color
                              : '#E5E7EB'
                        }
                      ]}
                    />
                  ))}
                </View>
              </View>
              
              <View style={styles.productivityContainer}>
                <ThemedText style={styles.productivityLabel}>
                  {activity.productivity}/5 ⭐
                </ThemedText>
              </View>
            </View>
          </LinearGradient>
        </Card>
      </View>
    );
  };

  return (
    <GradientBackground variant="ios">
      <StatusBar style={colorScheme === 'dark' ? 'light' : 'dark'} />
      
      {/* Header */}
      <View style={styles.header}>
        <View style={styles.headerTop}>
          <Pressable onPress={() => router.back()} style={styles.backButton}>
            <ThemedText style={styles.backButtonText}>←</ThemedText>
          </Pressable>
          <ThemedText style={styles.headerTitle}>Today's Timeline</ThemedText>
          <Pressable onPress={() => router.push('/add-activity')} style={styles.addButton}>
            <ThemedText style={styles.addButtonText}>+</ThemedText>
          </Pressable>
        </View>
        
        <ThemedText style={styles.dateText}>{formatDate(selectedDate)}</ThemedText>
        
        {/* Quick Stats */}
        <View style={styles.quickStats}>
          <Card variant="ios" style={styles.statCard}>
            <ThemedText style={styles.statNumber}>{timeline.length}</ThemedText>
            <ThemedText style={styles.statLabel}>Activities</ThemedText>
          </Card>
          <Card variant="ios" style={styles.statCard}>
            <ThemedText style={styles.statNumber}>{getTotalHours().toFixed(1)}h</ThemedText>
            <ThemedText style={styles.statLabel}>Total Time</ThemedText>
          </Card>
          <Card variant="ios" style={styles.statCard}>
            <ThemedText style={styles.statNumber}>{getProductiveHours().toFixed(1)}h</ThemedText>
            <ThemedText style={styles.statLabel}>Productive</ThemedText>
          </Card>
        </View>
      </View>

      {/* Timeline */}
      <ScrollView 
        style={styles.timelineContainer}
        showsVerticalScrollIndicator={false}
        contentContainerStyle={styles.timelineContent}
      >
        {timeline.map((activity, index) => renderTimelineItem(activity, index))}
        
        {/* Add Activity Button */}
        <View style={styles.addActivityContainer}>
          <Pressable 
            style={styles.addActivityButton}
            onPress={() => router.push('/add-activity')}
          >
            <LinearGradient
              colors={['#667eea', '#764ba2']}
              style={styles.addActivityGradient}
            >
              <ThemedText style={styles.addActivityText}>+ Add Activity</ThemedText>
            </LinearGradient>
          </Pressable>
        </View>
      </ScrollView>
    </GradientBackground>
  );
}

const styles = StyleSheet.create({
  header: {
    paddingTop: 60,
    paddingHorizontal: 20,
    paddingBottom: 20,
  },
  headerTop: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  backButton: {
    width: 44,
    height: 44,
    borderRadius: 22,
    backgroundColor: 'rgba(102, 126, 234, 0.1)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  backButtonText: {
    fontSize: 24,
    fontWeight: 'bold',
  },
  headerTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    flex: 1,
    textAlign: 'center',
  },
  addButton: {
    width: 44,
    height: 44,
    borderRadius: 22,
    backgroundColor: '#667eea',
    justifyContent: 'center',
    alignItems: 'center',
  },
  addButtonText: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#fff',
  },
  dateText: {
    fontSize: 18,
    textAlign: 'center',
    opacity: 0.8,
    marginBottom: 20,
  },
  quickStats: {
    flexDirection: 'row',
    gap: 12,
  },
  statCard: {
    flex: 1,
    alignItems: 'center',
    paddingVertical: 16,
  },
  statNumber: {
    fontSize: 24,
    fontWeight: 'bold',
    marginBottom: 4,
  },
  statLabel: {
    fontSize: 12,
    opacity: 0.7,
  },
  timelineContainer: {
    flex: 1,
  },
  timelineContent: {
    paddingHorizontal: 20,
    paddingBottom: 100,
  },
  timelineItem: {
    flexDirection: 'row',
    marginBottom: 20,
  },
  timelineLeft: {
    alignItems: 'center',
    marginRight: 16,
    width: 80,
  },
  timeContainer: {
    alignItems: 'center',
    marginBottom: 8,
  },
  timeText: {
    fontSize: 14,
    fontWeight: '600',
    opacity: 0.8,
  },
  timeDivider: {
    width: 1,
    height: 8,
    backgroundColor: '#E5E7EB',
    marginVertical: 2,
  },
  timelineDot: {
    width: 16,
    height: 16,
    borderRadius: 8,
    marginBottom: 8,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 4,
    elevation: 4,
  },
  timelineLine: {
    width: 2,
    flex: 1,
    backgroundColor: '#E5E7EB',
    opacity: 0.5,
  },
  activityCard: {
    flex: 1,
    padding: 0,
    overflow: 'hidden',
  },
  activityGradient: {
    padding: 16,
  },
  activityHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  activityTitleContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  categoryIcon: {
    fontSize: 24,
    marginRight: 12,
  },
  activityTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    flex: 1,
  },
  moodContainer: {
    backgroundColor: 'rgba(255, 255, 255, 0.8)',
    width: 32,
    height: 32,
    borderRadius: 16,
    justifyContent: 'center',
    alignItems: 'center',
  },
  moodText: {
    fontSize: 16,
  },
  activityDescription: {
    fontSize: 15,
    lineHeight: 20,
    opacity: 0.8,
    marginBottom: 16,
  },
  activityFooter: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  energyContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  energyLabel: {
    fontSize: 12,
    opacity: 0.7,
    marginRight: 8,
  },
  energyBars: {
    flexDirection: 'row',
    gap: 2,
  },
  energyBar: {
    width: 4,
    height: 12,
    borderRadius: 2,
  },
  productivityContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  productivityLabel: {
    fontSize: 12,
    fontWeight: '600',
    opacity: 0.8,
  },
  addActivityContainer: {
    marginTop: 20,
    alignItems: 'center',
  },
  addActivityButton: {
    borderRadius: 16,
    overflow: 'hidden',
    shadowColor: '#667eea',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.3,
    shadowRadius: 8,
    elevation: 8,
  },
  addActivityGradient: {
    paddingHorizontal: 32,
    paddingVertical: 16,
    alignItems: 'center',
  },
  addActivityText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: 'bold',
  },
});

import { useState, useEffect } from 'react';
import { StyleSheet, Pressable, ScrollView, Alert } from 'react-native';
import { useLocalSearchParams, router } from 'expo-router';
import { StatusBar } from 'expo-status-bar';

import { ThemedText } from '@/components/ThemedText';
import { ThemedView } from '@/components/ThemedView';
import { useJournal } from '@/context/JournalContext';

// Map mood values to emoji
const MOOD_EMOJIS = {
  'happy': '😊',
  'sad': '😔',
  'excited': '🤩',
  'anxious': '😰',
  'calm': '😌',
};

export default function EntryDetailScreen() {
  const { id } = useLocalSearchParams();
  const { getEntry, deleteEntry } = useJournal();
  const [entry, setEntry] = useState(null);

  useEffect(() => {
    if (id) {
      const foundEntry = getEntry(id.toString());
      if (foundEntry) {
        setEntry(foundEntry);
      }
    }
  }, [id, getEntry]);

  const handleDelete = () => {
    Alert.alert(
      "Delete Entry",
      "Are you sure you want to delete this entry? This action cannot be undone.",
      [
        { text: "Cancel", style: "cancel" },
        { 
          text: "Delete", 
          style: "destructive",
          onPress: async () => {
            if (id) {
              await deleteEntry(id.toString());
              router.back();
            }
          }
        }
      ]
    );
  };

  if (!entry) {
    return (
      <ThemedView style={styles.container}>
        <ThemedText>Entry not found</ThemedText>
      </ThemedView>
    );
  }

  return (
    <ThemedView style={styles.container}>
      <StatusBar style="auto" />
      
      <ThemedView style={styles.header}>
        <Pressable onPress={() => router.back()} style={styles.backButton}>
          <ThemedText style={styles.backButtonText}>Back</ThemedText>
        </Pressable>
        <ThemedView style={styles.headerActions}>
          <Pressable style={styles.deleteButton} onPress={handleDelete}>
            <ThemedText style={styles.deleteButtonText}>Delete</ThemedText>
          </Pressable>
        </ThemedView>
      </ThemedView>
      
      <ScrollView style={styles.content}>
        <ThemedView style={styles.entryHeader}>
          <ThemedText style={styles.dateText}>{entry.date}</ThemedText>
          <ThemedView style={styles.titleContainer}>
            <ThemedText style={styles.title}>{entry.title}</ThemedText>
            {entry.mood && (
              <ThemedText style={styles.moodEmoji}>
                {MOOD_EMOJIS[entry.mood]}
              </ThemedText>
            )}
          </ThemedView>
        </ThemedView>
        
        <ThemedText style={styles.contentText}>{entry.content}</ThemedText>
      </ScrollView>
    </ThemedView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#fff',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingTop: 60,
    paddingBottom: 15,
    borderBottomWidth: 1,
    borderBottomColor: '#eee',
  },
  backButton: {
    padding: 5,
  },
  backButtonText: {
    fontSize: 16,
    color: '#A1CEDC',
  },
  headerActions: {
    flexDirection: 'row',
  },
  deleteButton: {
    padding: 5,
  },
  deleteButtonText: {
    fontSize: 16,
    color: '#ff6b6b',
  },
  content: {
    flex: 1,
    padding: 20,
  },
  entryHeader: {
    marginBottom: 20,
  },
  dateText: {
    fontSize: 14,
    color: '#888',
    marginBottom: 8,
  },
  titleContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    flex: 1,
  },
  moodEmoji: {
    fontSize: 24,
    marginLeft: 10,
  },
  contentText: {
    fontSize: 16,
    lineHeight: 24,
  },
});

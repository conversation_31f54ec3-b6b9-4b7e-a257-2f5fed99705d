/**
 * Beautiful iOS-inspired color palette for the Daily Journal app
 * Features rich gradients and modern design aesthetics
 */

// iOS-style gradient colors
const iosBlue = ['#007AFF', '#5856D6'];
const iosPurple = ['#AF52DE', '#FF2D92'];
const iosOrange = ['#FF9500', '#FF2D92'];
const iosGreen = ['#34C759', '#30D158'];
const iosPink = ['#FF2D92', '#FF6B35'];

// Primary brand gradients
const primaryLight = ['#667eea', '#764ba2']; // Blue to Purple
const primaryDark = ['#8B5CF6', '#EC4899'];  // Purple to Pink

// Accent gradients
const accentLight = ['#ffecd2', '#fcb69f'];  // Warm peach
const accentDark = ['#ff9a9e', '#fecfef'];   // Pink gradient

export const Colors = {
  light: {
    // Text colors
    text: '#1D1D1F',           // iOS dark text
    textSecondary: '#8E8E93',  // iOS secondary text
    textMuted: '#C7C7CC',      // iOS tertiary text

    // Background gradients
    background: ['#F2F2F7', '#FFFFFF'], // iOS light background gradient
    backgroundSecondary: ['#FFFFFF', '#F9F9F9'], // Card background
    backgroundTertiary: ['#F8F8F8', '#EFEFEF'],  // Subtle gradient

    // Surface colors with gradients
    surface: ['#FFFFFF', '#FAFAFA'],
    surfaceSecondary: ['#F8FAFC', '#F1F5F9'],

    // Primary gradients
    primary: primaryLight,
    primaryGradient: ['#667eea', '#764ba2'],
    primaryLight: ['#818CF8', '#A78BFA'],
    primaryDark: ['#4F46E5', '#7C3AED'],

    // Accent gradients
    accent: accentLight,
    accentGradient: ['#ffecd2', '#fcb69f'],
    accentLight: ['#FCD34D', '#FBBF24'],
    accentDark: ['#D97706', '#B45309'],

    // Status colors
    success: '#10B981',
    warning: '#F59E0B',
    error: '#EF4444',
    info: '#3B82F6',

    // Mood colors
    moodHappy: '#FEF3C7',
    moodSad: '#DBEAFE',
    moodExcited: '#FCE7F3',
    moodAnxious: '#FEE2E2',
    moodCalm: '#D1FAE5',

    // Border colors
    border: '#E5E7EB',
    borderLight: '#F3F4F6',

    // Shadow colors
    shadow: 'rgba(0, 0, 0, 0.1)',
    shadowLight: 'rgba(0, 0, 0, 0.05)',

    // Legacy support
    tint: primaryLight,
    icon: '#6B7280',
    tabIconDefault: '#9CA3AF',
    tabIconSelected: primaryLight,
  },
  dark: {
    // Text colors
    text: '#F9FAFB',           // Off white
    textSecondary: '#D1D5DB',  // Light gray
    textMuted: '#9CA3AF',      // Medium gray

    // Background colors
    background: '#111827',      // Dark blue-gray
    backgroundSecondary: '#1F2937', // Lighter dark
    backgroundTertiary: '#374151',  // Medium dark

    // Surface colors
    surface: '#1F2937',
    surfaceSecondary: '#374151',

    // Primary colors
    primary: primaryDark,
    primaryLight: '#A78BFA',
    primaryDark: '#7C3AED',

    // Accent colors
    accent: accentDark,
    accentLight: '#FB923C',
    accentDark: '#EA580C',

    // Status colors
    success: '#34D399',
    warning: '#FBBF24',
    error: '#F87171',
    info: '#60A5FA',

    // Mood colors
    moodHappy: '#451A03',
    moodSad: '#1E3A8A',
    moodExcited: '#831843',
    moodAnxious: '#991B1B',
    moodCalm: '#064E3B',

    // Border colors
    border: '#4B5563',
    borderLight: '#374151',

    // Shadow colors
    shadow: 'rgba(0, 0, 0, 0.3)',
    shadowLight: 'rgba(0, 0, 0, 0.2)',

    // Legacy support
    tint: primaryDark,
    icon: '#9CA3AF',
    tabIconDefault: '#6B7280',
    tabIconSelected: primaryDark,
  },
};

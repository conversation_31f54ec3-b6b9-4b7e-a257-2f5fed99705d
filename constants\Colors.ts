/**
 * Beautiful color palette for the Daily Journal app
 * Inspired by modern design systems with warm, calming tones
 */

// Primary brand colors
const primaryLight = '#6366F1'; // Indigo
const primaryDark = '#8B5CF6';  // Purple

// Accent colors
const accentLight = '#F59E0B';  // Amber
const accentDark = '#F97316';   // Orange

export const Colors = {
  light: {
    // Text colors
    text: '#1F2937',           // Dark gray
    textSecondary: '#6B7280',  // Medium gray
    textMuted: '#9CA3AF',      // Light gray

    // Background colors
    background: '#FFFFFF',      // Pure white
    backgroundSecondary: '#F9FAFB', // Off white
    backgroundTertiary: '#F3F4F6',  // Light gray

    // Surface colors
    surface: '#FFFFFF',
    surfaceSecondary: '#F8FAFC',

    // Primary colors
    primary: primaryLight,
    primaryLight: '#818CF8',
    primaryDark: '#4F46E5',

    // Accent colors
    accent: accentLight,
    accentLight: '#FCD34D',
    accentDark: '#D97706',

    // Status colors
    success: '#10B981',
    warning: '#F59E0B',
    error: '#EF4444',
    info: '#3B82F6',

    // Mood colors
    moodHappy: '#FEF3C7',
    moodSad: '#DBEAFE',
    moodExcited: '#FCE7F3',
    moodAnxious: '#FEE2E2',
    moodCalm: '#D1FAE5',

    // Border colors
    border: '#E5E7EB',
    borderLight: '#F3F4F6',

    // Shadow colors
    shadow: 'rgba(0, 0, 0, 0.1)',
    shadowLight: 'rgba(0, 0, 0, 0.05)',

    // Legacy support
    tint: primaryLight,
    icon: '#6B7280',
    tabIconDefault: '#9CA3AF',
    tabIconSelected: primaryLight,
  },
  dark: {
    // Text colors
    text: '#F9FAFB',           // Off white
    textSecondary: '#D1D5DB',  // Light gray
    textMuted: '#9CA3AF',      // Medium gray

    // Background colors
    background: '#111827',      // Dark blue-gray
    backgroundSecondary: '#1F2937', // Lighter dark
    backgroundTertiary: '#374151',  // Medium dark

    // Surface colors
    surface: '#1F2937',
    surfaceSecondary: '#374151',

    // Primary colors
    primary: primaryDark,
    primaryLight: '#A78BFA',
    primaryDark: '#7C3AED',

    // Accent colors
    accent: accentDark,
    accentLight: '#FB923C',
    accentDark: '#EA580C',

    // Status colors
    success: '#34D399',
    warning: '#FBBF24',
    error: '#F87171',
    info: '#60A5FA',

    // Mood colors
    moodHappy: '#451A03',
    moodSad: '#1E3A8A',
    moodExcited: '#831843',
    moodAnxious: '#991B1B',
    moodCalm: '#064E3B',

    // Border colors
    border: '#4B5563',
    borderLight: '#374151',

    // Shadow colors
    shadow: 'rgba(0, 0, 0, 0.3)',
    shadowLight: 'rgba(0, 0, 0, 0.2)',

    // Legacy support
    tint: primaryDark,
    icon: '#9CA3AF',
    tabIconDefault: '#6B7280',
    tabIconSelected: primaryDark,
  },
};

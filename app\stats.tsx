import React, { useMemo } from 'react';
import { ScrollView, StyleSheet, View, Dimensions } from 'react-native';
import { router } from 'expo-router';
import { StatusBar } from 'expo-status-bar';

import { ThemedText } from '@/components/ThemedText';
import { ThemedView } from '@/components/ThemedView';
import { Card } from '@/components/Card';
import { Button } from '@/components/Button';
import { useJournal } from '@/context/JournalContext';

const { width } = Dimensions.get('window');

export default function StatsScreen() {
  const { entries, getMoodStats, getWritingStreak, getTotalWordCount } = useJournal();

  const stats = useMemo(() => {
    const moodStats = getMoodStats();
    const totalEntries = entries.length;
    const totalWords = getTotalWordCount();
    const streak = getWritingStreak();
    
    // Calculate average words per entry
    const avgWordsPerEntry = totalEntries > 0 ? Math.round(totalWords / totalEntries) : 0;
    
    // Calculate entries this month
    const currentMonth = new Date().getMonth();
    const currentYear = new Date().getFullYear();
    const entriesThisMonth = entries.filter(entry => {
      const entryDate = new Date(entry.createdAt || entry.date);
      return entryDate.getMonth() === currentMonth && entryDate.getFullYear() === currentYear;
    }).length;
    
    // Find most productive day of week
    const dayStats: { [key: number]: number } = {};
    entries.forEach(entry => {
      const day = new Date(entry.createdAt || entry.date).getDay();
      dayStats[day] = (dayStats[day] || 0) + 1;
    });
    
    const dayNames = ['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday'];
    const mostProductiveDay = Object.keys(dayStats).reduce((a, b) => 
      dayStats[parseInt(a)] > dayStats[parseInt(b)] ? a : b, '0'
    );
    
    // Calculate longest entry
    const longestEntry = entries.reduce((longest, entry) => 
      (entry.wordCount || 0) > (longest.wordCount || 0) ? entry : longest, 
      entries[0] || { wordCount: 0, title: 'No entries yet' }
    );
    
    return {
      totalEntries,
      totalWords,
      streak,
      avgWordsPerEntry,
      entriesThisMonth,
      mostProductiveDay: dayNames[parseInt(mostProductiveDay)],
      longestEntry,
      moodStats,
    };
  }, [entries, getMoodStats, getWritingStreak, getTotalWordCount]);

  const moodEmojis = {
    happy: '😊',
    sad: '😔',
    excited: '🤩',
    anxious: '😰',
    calm: '😌',
  };

  return (
    <ThemedView style={styles.container}>
      <StatusBar style="auto" />
      
      <View style={styles.header}>
        <Button
          title="← Back"
          onPress={() => router.back()}
          variant="ghost"
          size="small"
        />
        <ThemedText style={styles.title}>Journal Statistics</ThemedText>
        <View style={{ width: 60 }} />
      </View>

      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        {/* Overview Stats */}
        <View style={styles.section}>
          <ThemedText style={styles.sectionTitle}>Overview</ThemedText>
          <View style={styles.statsGrid}>
            <Card style={styles.statCard}>
              <ThemedText style={styles.statNumber}>{stats.totalEntries}</ThemedText>
              <ThemedText style={styles.statLabel}>Total Entries</ThemedText>
            </Card>
            <Card style={styles.statCard}>
              <ThemedText style={styles.statNumber}>{Math.round(stats.totalWords / 1000)}k</ThemedText>
              <ThemedText style={styles.statLabel}>Words Written</ThemedText>
            </Card>
            <Card style={styles.statCard}>
              <ThemedText style={styles.statNumber}>{stats.streak}</ThemedText>
              <ThemedText style={styles.statLabel}>Day Streak</ThemedText>
            </Card>
            <Card style={styles.statCard}>
              <ThemedText style={styles.statNumber}>{stats.avgWordsPerEntry}</ThemedText>
              <ThemedText style={styles.statLabel}>Avg Words/Entry</ThemedText>
            </Card>
          </View>
        </View>

        {/* This Month */}
        <View style={styles.section}>
          <ThemedText style={styles.sectionTitle}>This Month</ThemedText>
          <Card style={styles.monthCard}>
            <View style={styles.monthStat}>
              <ThemedText style={styles.monthNumber}>{stats.entriesThisMonth}</ThemedText>
              <ThemedText style={styles.monthLabel}>Entries Written</ThemedText>
            </View>
            <View style={styles.monthStat}>
              <ThemedText style={styles.monthNumber}>{stats.mostProductiveDay}</ThemedText>
              <ThemedText style={styles.monthLabel}>Most Productive Day</ThemedText>
            </View>
          </Card>
        </View>

        {/* Mood Distribution */}
        {Object.keys(stats.moodStats).length > 0 && (
          <View style={styles.section}>
            <ThemedText style={styles.sectionTitle}>Mood Distribution</ThemedText>
            <Card style={styles.moodCard}>
              {Object.entries(stats.moodStats).map(([mood, count]) => (
                <View key={mood} style={styles.moodItem}>
                  <View style={styles.moodInfo}>
                    <ThemedText style={styles.moodEmoji}>
                      {moodEmojis[mood as keyof typeof moodEmojis]}
                    </ThemedText>
                    <ThemedText style={styles.moodName}>
                      {mood.charAt(0).toUpperCase() + mood.slice(1)}
                    </ThemedText>
                  </View>
                  <View style={styles.moodStats}>
                    <ThemedText style={styles.moodCount}>{count}</ThemedText>
                    <View style={styles.moodBar}>
                      <View 
                        style={[
                          styles.moodBarFill, 
                          { width: `${(count / stats.totalEntries) * 100}%` }
                        ]} 
                      />
                    </View>
                  </View>
                </View>
              ))}
            </Card>
          </View>
        )}

        {/* Achievements */}
        <View style={styles.section}>
          <ThemedText style={styles.sectionTitle}>Achievements</ThemedText>
          <Card style={styles.achievementCard}>
            <View style={styles.achievement}>
              <ThemedText style={styles.achievementIcon}>🏆</ThemedText>
              <View style={styles.achievementText}>
                <ThemedText style={styles.achievementTitle}>Longest Entry</ThemedText>
                <ThemedText style={styles.achievementDescription}>
                  "{stats.longestEntry.title}" with {stats.longestEntry.wordCount || 0} words
                </ThemedText>
              </View>
            </View>
            
            {stats.streak >= 7 && (
              <View style={styles.achievement}>
                <ThemedText style={styles.achievementIcon}>🔥</ThemedText>
                <View style={styles.achievementText}>
                  <ThemedText style={styles.achievementTitle}>Week Warrior</ThemedText>
                  <ThemedText style={styles.achievementDescription}>
                    {stats.streak} days writing streak!
                  </ThemedText>
                </View>
              </View>
            )}
            
            {stats.totalWords >= 10000 && (
              <View style={styles.achievement}>
                <ThemedText style={styles.achievementIcon}>📚</ThemedText>
                <View style={styles.achievementText}>
                  <ThemedText style={styles.achievementTitle}>Word Master</ThemedText>
                  <ThemedText style={styles.achievementDescription}>
                    Over 10,000 words written!
                  </ThemedText>
                </View>
              </View>
            )}
          </Card>
        </View>
      </ScrollView>
    </ThemedView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingTop: 60,
    paddingBottom: 20,
  },
  title: {
    fontSize: 20,
    fontWeight: 'bold',
  },
  content: {
    flex: 1,
    paddingHorizontal: 20,
  },
  section: {
    marginBottom: 24,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 12,
  },
  statsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 12,
  },
  statCard: {
    width: (width - 56) / 2,
    alignItems: 'center',
    paddingVertical: 20,
  },
  statNumber: {
    fontSize: 28,
    fontWeight: 'bold',
    marginBottom: 4,
  },
  statLabel: {
    fontSize: 14,
    opacity: 0.7,
    textAlign: 'center',
  },
  monthCard: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    paddingVertical: 20,
  },
  monthStat: {
    alignItems: 'center',
  },
  monthNumber: {
    fontSize: 24,
    fontWeight: 'bold',
    marginBottom: 4,
  },
  monthLabel: {
    fontSize: 14,
    opacity: 0.7,
    textAlign: 'center',
  },
  moodCard: {
    padding: 16,
  },
  moodItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  moodInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  moodEmoji: {
    fontSize: 24,
    marginRight: 12,
  },
  moodName: {
    fontSize: 16,
    fontWeight: '500',
  },
  moodStats: {
    alignItems: 'flex-end',
    minWidth: 60,
  },
  moodCount: {
    fontSize: 16,
    fontWeight: 'bold',
    marginBottom: 4,
  },
  moodBar: {
    width: 60,
    height: 4,
    backgroundColor: '#E5E7EB',
    borderRadius: 2,
  },
  moodBarFill: {
    height: '100%',
    backgroundColor: '#6366F1',
    borderRadius: 2,
  },
  achievementCard: {
    padding: 16,
  },
  achievement: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 16,
  },
  achievementIcon: {
    fontSize: 32,
    marginRight: 16,
  },
  achievementText: {
    flex: 1,
  },
  achievementTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    marginBottom: 4,
  },
  achievementDescription: {
    fontSize: 14,
    opacity: 0.7,
  },
});
